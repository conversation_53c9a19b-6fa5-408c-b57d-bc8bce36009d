rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // SIMPLIFIED: Allow all authenticated users to read/write with size limits
    // This fixes the permission issues you're experiencing with image uploads
    match /{allPaths=**} {
      allow read: if true; // Public read access for all files
      allow write: if request.auth != null && 
                      request.resource.size < 100 * 1024 * 1024; // 100MB limit for media files
    }
  }
}
