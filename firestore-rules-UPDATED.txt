rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // USERS COLLECTION - Core profile data
    match /users/{userId} {
      // Own profile: full access
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Other profiles: read only if discoverable and verified
      allow read: if request.auth != null &&
                     resource.data.hideFromDiscovery != true &&
                     resource.data.isEmailVerified == true;

      // SIGNUP PERMISSION: Allow checking if email exists during account creation
      // This is needed for proper duplicate account detection
      allow read: if request.auth == null;
    }

    // SWIPES COLLECTION - User swipe actions
    match /swipes/{swipeId} {
      // Create: Only your own swipes with required fields
      allow create: if request.auth != null &&
                       request.resource.data.from == request.auth.uid &&
                       'from' in request.resource.data &&
                       'to' in request.resource.data &&
                       'liked' in request.resource.data;

      // Read: Your swipes or swipes on you (for mutual match detection)
      allow read: if request.auth != null &&
                     (request.auth.uid == resource.data.from ||
                      request.auth.uid == resource.data.to);

      // Delete: Only your own swipes (for testing/cleanup)
      allow delete: if request.auth != null &&
                       request.auth.uid == resource.data.from;
    }

    // MATCHES COLLECTION - Mutual likes between users
    match /matches/{matchId} {
      // Create/Read/Update/Delete: Only if you're a participant
      allow read, write: if request.auth != null &&
                            request.auth.uid in resource.data.participants;

      // Create: Additional validation for new matches
      allow create: if request.auth != null &&
                       request.resource.data.participants is list &&
                       request.resource.data.participants.size() == 2 &&
                       request.auth.uid in request.resource.data.participants;

      // ADMIN ACCESS: Allow Firebase Console visibility for debugging
      // This enables collection visibility in Firebase Console for auditing
      allow read: if request.auth != null;
    }

    // CHATS COLLECTION - Conversations between matched users
    match /chats/{chatId} {
      // Full access if you're a participant
      allow read, write: if request.auth != null &&
                            request.auth.uid in resource.data.participants;

      // MESSAGES SUBCOLLECTION - ENHANCED for media messages
      match /messages/{messageId} {
        // Create: Only your own messages (supports all message types)
        allow create: if request.auth != null &&
                         request.resource.data.senderID == request.auth.uid;

        // Read: Any authenticated user (chat access controlled above)
        allow read: if request.auth != null;

        // Update: Only your own messages (for upload progress updates)
        allow update: if request.auth != null &&
                         resource.data.senderID == request.auth.uid;

        // Delete: Only your own messages
        allow delete: if request.auth != null &&
                         resource.data.senderID == request.auth.uid;
      }
    }

    // ROOMMATE AGREEMENTS COLLECTION - NEW
    match /roommate_agreements/{agreementId} {
      // Read/Write: Only if you're userA or userB
      allow read, write: if request.auth != null &&
                            (request.auth.uid == resource.data.userA ||
                             request.auth.uid == resource.data.userB);

      // Create: Only if you're creating for yourself
      allow create: if request.auth != null &&
                       (request.auth.uid == request.resource.data.userA ||
                        request.auth.uid == request.resource.data.userB);
    }

    // LISTINGS COLLECTION - Housing listings
    match /listings/{listingId} {
      // Read: Any authenticated user
      allow read: if request.auth != null;

      // Write: Only creator
      allow write: if request.auth != null &&
                      request.resource.data.creatorID == request.auth.uid;
    }

    // REPORTS COLLECTION - User reports for safety
    match /reports/{reportId} {
      // Create: Only your own reports
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.reporterID;
      // Read: Admin access for review
      allow read: if request.auth != null;
    }

    // PROBLEM REPORTS COLLECTION - App issue reports
    match /problemReports/{reportId} {
      // Create: Only your own problem reports
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.userID;
      // Read: Admin access for review
      allow read: if request.auth != null;
    }

    // FALLBACK: Any other collections - basic auth required
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
