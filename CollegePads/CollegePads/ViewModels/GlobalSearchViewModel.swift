//
//  GlobalSearchViewModel.swift
//  CollegePads
//
//  This view model handles the logic for the Global Search feature.
//  It supports searching across users and listings using Firestore queries.
//  It updates published properties so the UI can react to changes in search state.
//

import SwiftUI
import FirebaseFirestore
import FirebaseAuth
import Combine

/// Defines the types of searches available.
enum SearchType: String, CaseIterable, Identifiable {
    case users = "Users"
    case listings = "Listings"
    var id: String { self.rawValue }
}



/// Simplified ViewModel for global search
@MainActor
class GlobalSearchViewModel: ObservableObject {
    @Published var query: String = "" {
        didSet {
            // Simple debounced search
            searchDebounceTimer?.invalidate()
            if !query.trimmingCharacters(in: .whitespaces).isEmpty {
                searchDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                    Task { await self.performSearch() }
                }
            } else {
                clearResults()
            }
        }
    }
    @Published var searchType: SearchType = .users {
        didSet {
            clearResults()
            if !query.trimmingCharacters(in: .whitespaces).isEmpty {
                Task { await performSearch() }
            }
        }
    }
    @Published var userResults: [UserModel] = []
    @Published var listingResults: [ListingModel] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var recentSearches: [String] = []
    @Published var searchSuggestions: [String] = []

    private var db = Firestore.firestore()
    private var searchDebounceTimer: Timer?

    private var currentUserID: String? {
        Auth.auth().currentUser?.uid
    }

    init() {
        loadRecentSearches()
        loadSearchSuggestions()
    }

    /// Enhanced search function with improved performance and error handling
    func performSearch() async {
        let searchQuery = query.trimmingCharacters(in: .whitespaces)
        guard !searchQuery.isEmpty else {
            await MainActor.run {
                clearResults()
            }
            return
        }

        // Minimum query length for performance
        guard searchQuery.count >= 2 else {
            await MainActor.run {
                self.errorMessage = "Please enter at least 2 characters to search"
                self.isLoading = false
            }
            return
        }

        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        print("🔍 GlobalSearch: Starting search for '\(searchQuery)' (type: \(searchType.rawValue))")

        do {
            if searchType == .users {
                await searchUsers(query: searchQuery)
            } else {
                await searchListings(query: searchQuery)
            }

            // Save to recent searches only on successful search
            await MainActor.run {
                if self.errorMessage == nil {
                    self.saveRecentSearch(searchQuery)
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Search failed: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ GlobalSearch: Search failed - \(error.localizedDescription)")
            }
        }
    }

    /// Search users with privacy protection and relevance ranking
    private func searchUsers(query: String) async {
        guard let currentUID = currentUserID else {
            await MainActor.run {
                self.errorMessage = "User not authenticated"
                self.isLoading = false
            }
            return
        }

        do {
            // Enhanced search approach - find users by name regardless of discovery settings
            print("🔍 GlobalSearch: Starting user search for query: '\(query)'")
            let snapshot = try await db.collection("users")
                .whereField("isEmailVerified", isEqualTo: true)
                // Removed hideFromDiscovery filter to allow searching hidden users by name
                .limit(to: 50) // Increased limit for better search coverage
                .getDocuments()

            print("📊 GlobalSearch: Retrieved \(snapshot.documents.count) users from Firestore")

            let allUsers = snapshot.documents.compactMap { doc -> UserModel? in
                do {
                    let user = try doc.data(as: UserModel.self)
                    print("🔍 GlobalSearch: Loaded user \(user.firstName ?? "Unknown") - Email verified: \(user.isEmailVerified)")
                    return user
                } catch {
                    print("❌ GlobalSearch: Failed to decode user from document \(doc.documentID): \(error)")
                    return nil
                }
            }

            // Enhanced client-side filtering with comprehensive name matching and mutual blocking
            let filteredUsers = allUsers.filter { user in
                guard let userID = user.id, userID != currentUID else { return false }

                // Check if current user has blocked this user
                if let blockedUsers = ProfileViewModel.shared.userProfile?.blockedUserIDs,
                   blockedUsers.contains(userID) {
                    print("⛔️ GlobalSearch: Excluding user blocked by current user: \(user.firstName ?? "Unknown") (\(userID))")
                    return false
                }

                // Check if this user has blocked the current user (mutual blocking check)
                if let userBlockedList = user.blockedUserIDs,
                   userBlockedList.contains(currentUID) {
                    print("⛔️ GlobalSearch: Excluding user who has blocked current user: \(user.firstName ?? "Unknown") (\(userID))")
                    return false
                }

                // Enhanced text matching with comprehensive name options
                let searchLower = query.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
                let firstName = user.firstName?.lowercased() ?? ""
                let lastName = user.lastName?.lowercased() ?? ""
                let fullName = "\(firstName) \(lastName)".trimmingCharacters(in: .whitespacesAndNewlines)
                let reverseName = "\(lastName) \(firstName)".trimmingCharacters(in: .whitespacesAndNewlines)
                let college = user.collegeName?.lowercased() ?? ""

                // Check for matches in various name combinations
                let nameMatch = firstName.contains(searchLower) ||
                                lastName.contains(searchLower) ||
                                fullName.contains(searchLower) ||
                                reverseName.contains(searchLower)

                // College match as secondary criteria
                let collegeMatch = college.contains(searchLower)

                // If it's a match, log the reason for debugging
                if nameMatch || collegeMatch {
                    var matchReason = nameMatch ? "name match" : "college match"
                    let visibilityStatus = user.hideFromDiscovery == true ? "hidden from discovery" : "visible in discovery"
                    print("✅ GlobalSearch: Including user \(user.firstName ?? "Unknown") (\(userID)): \(matchReason), \(visibilityStatus)")
                }

                return nameMatch || collegeMatch
            }

            await MainActor.run {
                self.userResults = filteredUsers
                self.isLoading = false
                print("✅ GlobalSearch: User search completed. Found \(filteredUsers.count) matching users")

                // Log summary of results
                let hiddenCount = filteredUsers.filter { $0.hideFromDiscovery == true }.count
                let visibleCount = filteredUsers.count - hiddenCount
                print("📊 GlobalSearch: Results breakdown - \(visibleCount) visible, \(hiddenCount) hidden from discovery")
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Search failed: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ GlobalSearch: User search failed - \(error.localizedDescription)")
            }
        }
    }

    /// Enhanced listing search with comprehensive matching
    private func searchListings(query: String) async {
        do {
            print("🔍 GlobalSearch: Starting listing search for query: '\(query)'")
            // Enhanced query for better coverage
            let snapshot = try await db.collection("listings")
                .limit(to: 50) // Increased limit for better search coverage
                .getDocuments()

            print("📊 GlobalSearch: Retrieved \(snapshot.documents.count) listings from Firestore")

            let allListings = snapshot.documents.compactMap { doc -> ListingModel? in
                try? doc.data(as: ListingModel.self)
            }

            // Enhanced client-side filtering with comprehensive matching
            let filteredListings = allListings.filter { listing in
                let searchLower = query.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
                let title = listing.title.lowercased()
                let address = listing.address.lowercased()

                // Enhanced matching including description if available
                var descriptionMatch = false
                if let description = listing.description {
                    descriptionMatch = description.lowercased().contains(searchLower)
                }

                let titleMatch = title.contains(searchLower)
                let addressMatch = address.contains(searchLower)

                // Log matches for debugging
                if titleMatch || addressMatch || descriptionMatch {
                    var matchReason = titleMatch ? "title" : (addressMatch ? "address" : "description")
                    print("✅ GlobalSearch: Including listing '\(listing.title)': \(matchReason) match")
                }

                return titleMatch || addressMatch || descriptionMatch
            }

            await MainActor.run {
                self.listingResults = filteredListings
                self.isLoading = false
                print("✅ GlobalSearch: Listing search completed. Found \(filteredListings.count) matching listings")
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Listing search failed: \(error.localizedDescription)"
                self.isLoading = false
                print("❌ GlobalSearch: Listing search failed - \(error.localizedDescription)")
            }
        }
    }



    // MARK: - Helper Methods

    /// Clear all search results
    private func clearResults() {
        userResults = []
        listingResults = []
        errorMessage = nil
    }

    /// Save search query to recent searches
    private func saveRecentSearch(_ query: String) {
        guard !query.isEmpty else { return }

        // Remove if already exists
        recentSearches.removeAll { $0.lowercased() == query.lowercased() }

        // Add to beginning
        recentSearches.insert(query, at: 0)

        // Keep only last 10 searches
        if recentSearches.count > 10 {
            recentSearches = Array(recentSearches.prefix(10))
        }

        // Save to UserDefaults
        UserDefaults.standard.set(recentSearches, forKey: "RecentSearches")
    }

    /// Load recent searches from UserDefaults
    private func loadRecentSearches() {
        recentSearches = UserDefaults.standard.stringArray(forKey: "RecentSearches") ?? []
    }

    /// Load search suggestions (could be enhanced with server-side suggestions)
    private func loadSearchSuggestions() {
        // Basic suggestions - could be enhanced with popular searches from server
        searchSuggestions = [
            "Computer Science",
            "Engineering",
            "Business",
            "Psychology",
            "Biology",
            "Mathematics",
            "English",
            "History"
        ]
    }

    /// Clear recent searches
    func clearRecentSearches() {
        recentSearches = []
        UserDefaults.standard.removeObject(forKey: "RecentSearches")
    }

    /// Manual search trigger (for button press)
    func triggerSearch() {
        Task {
            await performSearch()
        }
    }


}