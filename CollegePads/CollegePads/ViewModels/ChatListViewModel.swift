//
//  ChatsListViewModel.swift
//  CollegePads
//
//  Updated to include loading state and sort chats by creation date (newest first).
//

import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift
import FirebaseAuth
import Combine

struct ChatListItem: Identifiable {
    let id: String
    let participants: [String]
    let createdAt: Date
    let lastMessage: String
    let lastMessageAt: Date
    let lastMessageSenderID: String
    let unreadCount: [String: Int]
    let isActive: Bool

    /// Get unread count for specific user
    func getUnreadCount(for userID: String) -> Int {
        return unreadCount[userID] ?? 0
    }

    /// Get the other participant's ID
    func getOtherParticipant(currentUserID: String) -> String? {
        return participants.first { $0 != currentUserID }
    }
}

class ChatsListViewModel: ObservableObject {
    @Published var chats: [ChatListItem] = []
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false  // New loading state

    private var cancellables = Set<AnyCancellable>()
    private let db = Firestore.firestore()
    private var refreshTimer: Timer?

    var currentUserID: String? {
        Auth.auth().currentUser?.uid
    }

    /// Force refresh when messages tab is opened - always gets fresh data
    func forceRefreshOnTabOpen() {
        print("💬 ChatsListViewModel: Force refresh on tab open - ensuring fresh data")

        // Clear current data to show loading state
        DispatchQueue.main.async {
            self.chats = []
            self.errorMessage = nil
            self.isLoading = true
        }

        // Force fresh data load with timeout protection
        fetchChats()

        // Add timeout protection to prevent infinite loading
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
            if self.isLoading && self.chats.isEmpty {
                print("⚠️ ChatsListViewModel: Loading timeout - stopping loading state")
                self.isLoading = false
                self.errorMessage = "Loading timeout. Please try again."
            }
        }
    }

    /// Fetches chats where the current user is a participant with enhanced metadata
    func fetchChats() {
        guard let uid = currentUserID else {
            self.errorMessage = "User not authenticated."
            self.isLoading = false
            return
        }

        print("💬 ChatsListViewModel: Fetching chats for user: \(uid)")
        // Always show loading when explicitly fetching
        isLoading = true

        db.collection("chats")
            .whereField("participants", arrayContains: uid)
            .snapshotPublisher()
            .map { snapshot -> [ChatListItem] in
                print("💬 ChatsListViewModel: Received \(snapshot.documents.count) chat documents")

                let items = snapshot.documents.compactMap { doc -> ChatListItem? in
                    let data = doc.data()

                    guard let participants = data["participants"] as? [String],
                          let createdAtTimestamp = (data["createdAt"] as? Timestamp)?.dateValue() else {
                        print("❌ ChatsListViewModel: Missing required fields in chat: \(doc.documentID)")
                        return nil
                    }

                    // Extract metadata with defaults
                    let lastMessage = data["lastMessage"] as? String ?? ""
                    let lastMessageAt = (data["lastMessageAt"] as? Timestamp)?.dateValue() ?? createdAtTimestamp
                    let lastMessageSenderID = data["lastMessageSenderID"] as? String ?? ""
                    let unreadCount = data["unreadCount"] as? [String: Int] ?? [:]
                    let isActive = data["isActive"] as? Bool ?? true

                    // Only include active chats with messages or recent creation
                    if !isActive || (lastMessage.isEmpty && Date().timeIntervalSince(createdAtTimestamp) > 86400) {
                        return nil // Skip inactive chats or old empty chats
                    }

                    print("✅ ChatsListViewModel: Parsed chat \(doc.documentID) with last message: '\(lastMessage)'")

                    return ChatListItem(
                        id: doc.documentID,
                        participants: participants,
                        createdAt: createdAtTimestamp,
                        lastMessage: lastMessage,
                        lastMessageAt: lastMessageAt,
                        lastMessageSenderID: lastMessageSenderID,
                        unreadCount: unreadCount,
                        isActive: isActive
                    )
                }

                // Sort chats by last message time (most recent first)
                let sortedItems = items.sorted { $0.lastMessageAt > $1.lastMessageAt }
                print("💬 ChatsListViewModel: Returning \(sortedItems.count) sorted chats")
                return sortedItems
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                self?.isLoading = false
                if case let .failure(error) = completion {
                    print("❌ ChatsListViewModel: Error fetching chats: \(error.localizedDescription)")
                    self?.errorMessage = "Failed to load chats: \(error.localizedDescription)"
                }
            } receiveValue: { [weak self] chats in
                print("💬 ChatsListViewModel: Updated chat list with \(chats.count) chats")
                self?.isLoading = false
                self?.chats = chats
                self?.errorMessage = nil

                // Debug: Log chat details if empty
                if chats.isEmpty {
                    print("⚠️ ChatsListViewModel: No chats found - this may be normal for new users")
                } else {
                    print("✅ ChatsListViewModel: Successfully loaded \(chats.count) chats")
                }
            }
            .store(in: &cancellables)
    }

    /// Manual method to clear loading state if stuck
    func clearLoadingState() {
        print("🔄 ChatsListViewModel: Manually clearing loading state")
        DispatchQueue.main.async {
            self.isLoading = false
        }
    }

    // MARK: - Auto-Refresh Management

    func startAutoRefresh() {
        stopAutoRefresh() // Stop any existing timer

        // Refresh every 60 seconds for chat updates (reduced from 20s to avoid excessive loading)
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            print("💬 ChatsListViewModel: Auto-refreshing chats (background)")
            // Only refresh if not currently loading to avoid conflicts
            if self?.isLoading != true {
                self?.fetchChats()
            } else {
                print("⚠️ ChatsListViewModel: Skipping auto-refresh - already loading")
            }
        }

        print("✅ ChatsListViewModel: Auto-refresh started (every 60 seconds)")
    }

    func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
        print("🛑 ChatsListViewModel: Auto-refresh stopped")
    }

    // MARK: - Lifecycle Management

    deinit {
        stopAutoRefresh()
        cancellables.removeAll()
    }
}
