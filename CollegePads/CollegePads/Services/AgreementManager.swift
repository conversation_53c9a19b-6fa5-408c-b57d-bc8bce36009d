import Foundation
import FirebaseFirestore
import FirebaseAuth
import Combine

@MainActor
class AgreementManager: ObservableObject {
    static let shared = AgreementManager()
    
    private let db = Firestore.firestore()
    
    @Published var agreements: [RoommateAgreement] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private init() {}
    
    // MARK: - Agreement Creation
    
    func createAgreement(
        matchID: String,
        userA: String,
        userB: String,
        template: AgreementTemplate,
        customSections: [AgreementSection] = [],
        title: String = ""
    ) async throws -> String {
        
        isLoading = true
        defer { isLoading = false }
        
        let agreementID = UUID().uuidString
        let sections = customSections.isEmpty ? template.sections : customSections
        
        let agreement = RoommateAgreement(
            id: agreementID,
            matchID: matchID,
            userA: userA,
            userB: userB,
            title: title.isEmpty ? "\(template.title) Roommate Agreement" : title,
            templateType: template.rawValue,
            sections: sections,
            signatures: RoommateAgreement.Signatures(
                userA: RoommateAgreement.Signature(signed: false, signedAt: nil, ipAddress: nil),
                userB: RoommateAgreement.Signature(signed: false, signedAt: nil, ipAddress: nil)
            ),
            status: .draft,
            createdAt: Date(),
            updatedAt: Date(),
            expiresAt: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()
        )
        
        try await saveAgreement(agreement)
        
        // Add to local state
        agreements.append(agreement)
        
        print("✅ Agreement created successfully: \(agreementID)")
        return agreementID
    }
    
    // MARK: - Agreement Signing
    
    func signAgreement(agreementID: String, userID: String, signature: String) async throws {
        isLoading = true
        defer { isLoading = false }
        
        let agreementRef = db.collection("roommate_agreements").document(agreementID)
        
        // Get current agreement
        let document = try await agreementRef.getDocument()
        guard let agreementData = try? document.data(as: RoommateAgreement.self) else {
            throw AgreementError.agreementNotFound
        }
        var agreement = agreementData
        
        // Update signature
        let signatureData = RoommateAgreement.Signature(
            signed: true,
            signedAt: Date(),
            ipAddress: await getCurrentIPAddress()
        )
        
        if userID == agreement.userA {
            agreement.signatures.userA = signatureData
        } else if userID == agreement.userB {
            agreement.signatures.userB = signatureData
        } else {
            throw AgreementError.unauthorizedUser
        }
        
        // Check if both users have signed
        if agreement.signatures.userA.signed && agreement.signatures.userB.signed {
            agreement.status = .signed
        } else {
            agreement.status = .pending
        }
        
        agreement.updatedAt = Date()
        
        // Save to Firebase
        try await saveAgreement(agreement)
        
        // Update local state
        if let index = agreements.firstIndex(where: { $0.id == agreementID }) {
            agreements[index] = agreement
        }
        
        print("✅ Agreement signed by user: \(userID)")
        
        // Generate PDF if both users have signed
        if agreement.status == .signed {
            try await generateAgreementPDF(agreement)
        }
    }
    
    // MARK: - Agreement Retrieval
    
    func loadAgreements(for userID: String) async throws {
        isLoading = true
        defer { isLoading = false }
        
        let snapshot = try await db.collection("roommate_agreements")
            .whereField("userA", isEqualTo: userID)
            .getDocuments()
        
        let snapshot2 = try await db.collection("roommate_agreements")
            .whereField("userB", isEqualTo: userID)
            .getDocuments()
        
        var loadedAgreements: [RoommateAgreement] = []
        
        // Process both queries
        for document in snapshot.documents + snapshot2.documents {
            do {
                let agreement = try document.data(as: RoommateAgreement.self)
                loadedAgreements.append(agreement)
            } catch {
                print("❌ Failed to decode agreement: \(error)")
            }
        }
        
        // Remove duplicates and sort by creation date
        let uniqueAgreements = Array(Set(loadedAgreements)).sorted { $0.createdAt > $1.createdAt }
        
        agreements = uniqueAgreements
        print("✅ Loaded \(agreements.count) agreements for user: \(userID)")
    }
    
    // MARK: - Helper Methods
    
    private func saveAgreement(_ agreement: RoommateAgreement) async throws {
        try await db.collection("roommate_agreements")
            .document(agreement.id)
            .setData(from: agreement)
    }
    
    private func getCurrentIPAddress() async -> String {
        // Simple IP detection - in production you might want a more robust solution
        return "127.0.0.1" // Placeholder
    }
    
    private func generateAgreementPDF(_ agreement: RoommateAgreement) async throws {
        // TODO: Implement PDF generation
        print("📄 Generating PDF for agreement: \(agreement.id)")
        
        // This would typically involve:
        // 1. Creating a PDF document with the agreement content
        // 2. Uploading to Firebase Storage
        // 3. Updating the agreement with the PDF URL
        
        // For now, just update with a placeholder URL
        let pdfURL = "https://example.com/agreements/\(agreement.id).pdf"
        
        try await db.collection("roommate_agreements")
            .document(agreement.id)
            .updateData(["pdfUrl": pdfURL])
        
        print("✅ PDF generated and saved: \(pdfURL)")
    }
}

// MARK: - Supporting Types

struct RoommateAgreement: Identifiable, Codable, Hashable, Equatable {
    let id: String
    let matchID: String
    let userA: String
    let userB: String
    var title: String
    let templateType: String
    var sections: [AgreementSection]
    var signatures: Signatures
    var status: AgreementStatus
    let createdAt: Date
    var updatedAt: Date
    let expiresAt: Date
    var pdfUrl: String?

    struct Signatures: Codable, Hashable, Equatable {
        var userA: Signature
        var userB: Signature
    }

    struct Signature: Codable, Hashable, Equatable {
        let signed: Bool
        let signedAt: Date?
        let ipAddress: String?
    }

    // Implement Equatable
    static func == (lhs: RoommateAgreement, rhs: RoommateAgreement) -> Bool {
        return lhs.id == rhs.id
    }

    // Implement Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    var isFullySigned: Bool {
        return signatures.userA.signed && signatures.userB.signed
    }
    
    var isExpired: Bool {
        return Date() > expiresAt
    }
}

enum AgreementStatus: String, Codable, CaseIterable {
    case draft = "draft"
    case pending = "pending"
    case signed = "signed"
    case expired = "expired"
    
    var displayName: String {
        switch self {
        case .draft: return "Draft"
        case .pending: return "Pending Signature"
        case .signed: return "Signed"
        case .expired: return "Expired"
        }
    }
    
    var color: String {
        switch self {
        case .draft: return "gray"
        case .pending: return "orange"
        case .signed: return "green"
        case .expired: return "red"
        }
    }
}

enum AgreementStep: CaseIterable {
    case template
    case customize
    case review
    case sign
    
    var title: String {
        switch self {
        case .template: return "Choose Template"
        case .customize: return "Customize Agreement"
        case .review: return "Review Agreement"
        case .sign: return "Sign Agreement"
        }
    }
}

enum AgreementError: LocalizedError {
    case agreementNotFound
    case unauthorizedUser
    case alreadySigned
    case expired
    
    var errorDescription: String? {
        switch self {
        case .agreementNotFound:
            return "Agreement not found"
        case .unauthorizedUser:
            return "You are not authorized to sign this agreement"
        case .alreadySigned:
            return "Agreement has already been signed"
        case .expired:
            return "Agreement has expired"
        }
    }
}
