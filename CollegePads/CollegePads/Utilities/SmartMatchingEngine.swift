import Foundation
import CoreLocation

struct SmartMatchingEngine {

    /// 1) Compatibility + bonus
    static func calculateSmartMatchScore(
        between me: UserModel, and them: UserModel,
        averageRating: Double? = nil
    ) -> Double {
        let base = CompatibilityCalculator.calculateUserCompatibility(between: me, and: them)
        var bonus = 0.0

        // Verification bonus - simplified to use only isEmailVerified
        if me.isEmailVerified && them.isEmailVerified { bonus += 10 }

        if me.housingStatus?.lowercased() == them.housingStatus?.lowercased() { bonus += 5 }
        if me.leaseDuration?.lowercased() == them.leaseDuration?.lowercased() { bonus += 5 }
        if let r = averageRating { bonus += r * 2 }
        return min(base + bonus, 100.0)
    }

    /// 2) Filter-based match count
    static func calculateFilterMatchScore(
        filterSettings s: FilterSettings,
        otherUser u: UserModel,
        currentUser me: UserModel
    ) -> Int {
        var score = 0
        let userName = u.firstName ?? "Unknown"

        // ENHANCED DEBUGGING: Start detailed score calculation logging
        print("🔍 SmartMatchingEngine: Starting score calculation for \(userName)")
        print("   - Initial score: \(score)")
        print("   - Show All Profiles mode: \(s.showAllProfiles ?? false)")
        print("   - Filter mode: \(s.mode ?? "none")")

        // CRITICAL FIX: Special handling for "Show All Profiles" mode - ensure NO profiles are filtered out
        if s.showAllProfiles == true {
            // In "Show All Profiles" mode, we want to show ALL users based on compatibility rather than strict filter matching
            // Give a substantial base score to ensure ALL users are included, then add compatibility bonuses
            score = 50 // MUCH HIGHER base score to ensure user is ALWAYS included in "Show All Profiles" mode

            print("🔍 SmartMatchingEngine: 'Show All Profiles' mode - giving base score of \(score) to \(userName) to GUARANTEE inclusion")

            // Add compatibility bonus for ranking but don't subtract anything
            let compatibilityScore = CompatibilityCalculator.calculateUserCompatibility(between: me, and: u)
            score += Int(compatibilityScore / 10) // Add compatibility as bonus for ranking

            print("🔍 SmartMatchingEngine: 'Show All Profiles' mode - final score for \(userName): \(score) (guaranteed > 0)")
            return score // Return immediately to skip all filtering logic
        }

        // CRITICAL FIX: Normal filtering logic for non-"Show All Profiles" mode
        // 2-a) housing pairing rules - compare YOUR filter choice to THEIR profile fields
        print("   - Applying housing status filtering")

        // Check if we have housing status requirements
        if let mine = s.housingStatus {
            guard let theirsProfile = u.housingStatus else {
                print("   - ❌ EXCLUSION: \(userName) has no housing status")
                return 0
            }

            print("   - My housing status: \(mine)")
            print("   - \(userName)'s housing status: \(theirsProfile)")

            // Apply housing compatibility logic
            switch mine {
            case PrimaryHousingPreference.lookingForRoommate.rawValue:
                if theirsProfile == PrimaryHousingPreference.lookingForLease.rawValue {
                    score += 2
                    print("   - ✅ Housing compatibility: Looking for roommate + Looking for lease (+2)")
                }
            case PrimaryHousingPreference.lookingForLease.rawValue:
                if theirsProfile == PrimaryHousingPreference.lookingForRoommate.rawValue {
                    score += 2
                    print("   - ✅ Housing compatibility: Looking for lease + Looking for roommate (+2)")
                }
            case PrimaryHousingPreference.lookingToFindTogether.rawValue:
                let okStatuses = [
                    PrimaryHousingPreference.lookingToFindTogether.rawValue,
                    PrimaryHousingPreference.lookingForLease.rawValue
                ]
                if okStatuses.contains(theirsProfile) {
                    score += 2
                    print("   - ✅ Housing compatibility: Looking to find together + compatible status (+2)")
                }
            default:
                break
            }
        } else {
            print("   - No housing status filter specified, applying compatibility bonus only")
        }

        // Apply additional filter criteria for both "Show All Profiles" mode and specific modes
        // 2-b) college - only apply if not in "Show All Profiles" mode or if college is specifically set
        if s.showAllProfiles != true {
            if let mine = s.collegeName?.trimmingCharacters(in: .whitespacesAndNewlines).lowercased(),
               let theirs = u.collegeName?.trimmingCharacters(in: .whitespacesAndNewlines).lowercased(),
               mine == theirs {
              score += 1
            }
        } else {
            // In "Show All Profiles" mode, give bonus for college match but don't require it
            if let mine = s.collegeName?.trimmingCharacters(in: .whitespacesAndNewlines).lowercased(),
               !mine.isEmpty,
               let theirs = u.collegeName?.trimmingCharacters(in: .whitespacesAndNewlines).lowercased(),
               mine == theirs {
              score += 1
            }
        }

        // 2-c) distance - only apply if in distance mode
        if s.mode == FilterMode.distance.rawValue,
           let maxKm = s.maxDistance,
           let myGeo = me.location, let theirGeo = u.location {
            let dist = CLLocation(latitude: theirGeo.latitude,
                                  longitude: theirGeo.longitude)
                .distance(from: CLLocation(latitude: myGeo.latitude,
                                           longitude: myGeo.longitude)) / 1000
            if dist <= maxKm { score += 1 }
        }

        // 2-d) grade - apply as bonus in "All" mode, strict in other modes
        if let g = s.gradeGroup?.lowercased(), !g.isEmpty,
           let ug = u.gradeLevel?.lowercased() {
            let gradeMatches = switch g {
            case "freshman" where ug == "freshman": true
            case "underclassmen" where ["freshman","sophomore"].contains(ug): true
            case "upperclassmen" where ["junior","senior"].contains(ug): true
            case "graduate" where ug == "graduate": true
            default: false
            }
            if gradeMatches { score += 1 }
        }

        // 2-e) room type - apply as bonus
        if let roomType = s.roomType, !roomType.isEmpty, roomType == u.roomType {
            score += 1
        }

        // 2-f) amenities - apply as bonus
        if let want = s.amenities, !want.isEmpty, let have = u.amenities,
           Set(want).isSubset(of: Set(have)) {
            score += 1
        }

        // 2-g) cleanliness & sleep - apply as bonus
        if let cleanliness = s.cleanliness, cleanliness == u.cleanliness {
            score += 1
        }
        if let sleepSchedule = s.sleepSchedule?.lowercased(), !sleepSchedule.isEmpty,
           sleepSchedule == u.sleepSchedule?.lowercased() {
            score += 1
        }

        // 2-h) gender - apply as bonus
        if let preferredGender = s.preferredGender, !preferredGender.isEmpty,
           preferredGender == u.gender {
            score += 1
        }

        // 2-i) age diff - apply as bonus
        if let Δ = s.maxAgeDifference, Δ > 0,
           let bd1 = me.dateOfBirth, let bd2 = u.dateOfBirth,
           let d1 = ISO8601DateFormatter().date(from: bd1),
           let d2 = ISO8601DateFormatter().date(from: bd2) {
            let yrs = abs(Calendar.current
                .dateComponents([.year], from: d2, to: d1).year ?? 0)
            if yrs <= Int(Δ) { score += 1 }
        }

        // 2-j) lifestyle toggles - apply as bonus

        // Pet friendly & smoker are both Bool?
        if let wantPet = s.petFriendly,
           let hasPet  = u.petFriendly,
           wantPet == hasPet {
            score += 1
        }

        if let wantSmoke = s.smoker,
           let isSmoker  = u.smoker,
           wantSmoke == isSmoker {
            score += 1
        }

        // Drinker → user.drinking is String?
        if let wantDrink = s.drinker,
           let drinkVal  = u.drinking?.lowercased() {
            // wantDrink==true means user must *not* be "not for me"
            if wantDrink ? (drinkVal != "not for me")
                : (drinkVal == "not for me") {
                score += 1
            }
        }

        // Marijuana → user.cannabis is String?
        if let wantMj     = s.marijuana,
           let cannabisVal = u.cannabis?.lowercased() {
            if wantMj ? (cannabisVal != "never")
                : (cannabisVal == "never") {
                score += 1
            }
        }

        // Workout → user.workout is String?
        if let wantWorkout = s.workout,
           let workoutVal  = u.workout?.lowercased() {
            if wantWorkout ? (workoutVal != "never")
                : (workoutVal == "never") {
                score += 1
            }
        }

        // 2-k) interests (comma-split vs array intersection) - apply as bonus
        if let wants = s.interests?
            .split(separator: ",")
            .map({ $0.trimmingCharacters(in: .whitespaces) }),
           !wants.isEmpty,
           let haves = u.interests,
           !Set(wants).isDisjoint(with: Set(haves)) {
            score += 1
        }

        // ENHANCED DEBUGGING: Final score summary
        print("🔍 SmartMatchingEngine: Final score for \(userName): \(score)")
        if score <= 0 {
            print("   - ⚠️ WARNING: Score is \(score), user will be EXCLUDED from results")
        } else {
            print("   - ✅ Score is \(score), user will be INCLUDED in results")
        }

        return score
    }

    /// 3) churn out only those > 0, sorted by descending score
    static func generateSortedMatches(
      from all: [UserModel],
      currentUser me: UserModel
    ) -> [UserModel] {
      let myBlockedUsers = Set(me.blockedUserIDs ?? [])
      let myUserID = me.id ?? ""

      // 1) remove self, users I blocked, and users who blocked me (mutual blocking)
      let candidates = all
        .filter { user in
            guard let userID = user.id, userID != me.id else { return false }

            // Check if I blocked them
            if myBlockedUsers.contains(userID) { return false }

            // Check if they blocked me (mutual blocking)
            if let theirBlockedUsers = user.blockedUserIDs,
               theirBlockedUsers.contains(myUserID) { return false }

            return true
        }

      // 2) if user has explicit FilterSettings, use them…
      if let fs = me.filterSettings {
        return candidates
          .map { user in
            (user, calculateFilterMatchScore(
                       filterSettings: fs,
                       otherUser:    user,
                       currentUser:  me))
          }
          // after
          .filter { $0.1 > 0 }
          .sorted { lhs, rhs in
            if lhs.1 != rhs.1 {
              return lhs.1 > rhs.1                    // primary: filter score
            } else {
              // secondary: overall compatibility
              let scoreL = calculateSmartMatchScore(between: me, and: lhs.0)
              let scoreR = calculateSmartMatchScore(between: me, and: rhs.0)
              return scoreL > scoreR
            }
          }
          .map { $0.0 }
      }

      // 3) …otherwise, fall back to pure compatibility sorting
      return candidates
        .sorted {
          calculateSmartMatchScore(between: me, and: $0)
          >
          calculateSmartMatchScore(between: me, and: $1)
        }
    }
}

// New overload that takes a pre-built FilterSettings
extension SmartMatchingEngine {
  static func generateSortedMatches(
    from all: [UserModel],
    currentUser me: UserModel,
    using fs: FilterSettings
  ) -> [UserModel] {
    let myBlockedUsers = Set(me.blockedUserIDs ?? [])
    let myUserID = me.id ?? ""

    // ENHANCED DEBUGGING: Track the filtering pipeline
    print("🔍 SmartMatchingEngine.generateSortedMatches: Starting with \(all.count) users")
    print("   - Show All Profiles mode: \(fs.showAllProfiles ?? false)")
    print("   - Filter mode: \(fs.mode ?? "none")")

    let candidates = all.filter { user in
        guard let userID = user.id else { return false }

        let isNotSelf = userID != me.id
        let isNotBlockedByMe = !myBlockedUsers.contains(userID)
        let hasNotBlockedMe = !(user.blockedUserIDs?.contains(myUserID) ?? false)
        let hasProfileImage = hasValidProfileImage(user)

        let isEligible = isNotSelf && isNotBlockedByMe && hasNotBlockedMe && hasProfileImage

        if !isEligible && userID != me.id {
            let reasons = [
                !isNotBlockedByMe ? "blocked by me" : nil,
                !hasNotBlockedMe ? "has blocked me" : nil,
                !hasProfileImage ? "no profile image" : nil
            ].compactMap { $0 }.joined(separator: ", ")
            if !reasons.isEmpty {
                print("   ⚠️ Excluding \(user.firstName ?? "Unknown"): \(reasons)")
            }
        }

        return isEligible
    }
    print("🔍 SmartMatchingEngine: After removing self, blocked users, and users without profile images: \(candidates.count)")

    let scoredUsers = candidates.map { user in
        let score = calculateFilterMatchScore(filterSettings: fs, otherUser: user, currentUser: me)
        return (user, score)
    }
    print("🔍 SmartMatchingEngine: Scored \(scoredUsers.count) users")

    // Log score distribution
    let scoreDistribution = Dictionary(grouping: scoredUsers, by: { $0.1 })
    for (score, users) in scoreDistribution.sorted(by: { $0.key > $1.key }) {
        print("   - Score \(score): \(users.count) users")
    }

    let filteredUsers = scoredUsers.filter { $0.1 > 0 }
    print("🔍 SmartMatchingEngine: After filtering score > 0: \(filteredUsers.count)")

    if filteredUsers.count < scoredUsers.count {
        let excludedCount = scoredUsers.count - filteredUsers.count
        print("   - ⚠️ EXCLUDED \(excludedCount) users with score ≤ 0")

        // Log specific excluded users for debugging
        let excludedUsers = scoredUsers.filter { $0.1 <= 0 }
        for (user, score) in excludedUsers.prefix(5) { // Show first 5 excluded users
            print("     - \(user.firstName ?? "Unknown") (score: \(score))")
        }
        if excludedUsers.count > 5 {
            print("     - ... and \(excludedUsers.count - 5) more")
        }

        // Special warning for Show All Profiles mode
        if fs.showAllProfiles == true {
            print("   - 🚨 CRITICAL: Show All Profiles mode should NOT exclude any users!")
        }
    }

    let sortedUsers = filteredUsers.sorted { $0.1 > $1.1 }
    let finalResult = sortedUsers.map { $0.0 }

    print("🔍 SmartMatchingEngine: Final result: \(finalResult.count) users")

    return finalResult
  }

  /// Simplified profile image validation - check if user has at least 1 image total
  private static func hasValidProfileImage(_ user: UserModel) -> Bool {
      var imageCount = 0

      // Count legacy single image
      if let imageUrl = user.profileImageUrl, !imageUrl.isEmpty {
          imageCount += 1
      }

      // Count multiple images array
      if let imageUrls = user.profileImageUrls {
          imageCount += imageUrls.filter { !$0.isEmpty }.count
      }

      return imageCount >= 1
  }
}
