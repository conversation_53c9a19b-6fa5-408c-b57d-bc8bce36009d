import UIKit
import Firebase
import FirebaseAnalytics
import FirebaseMessaging
import FirebaseInAppMessaging
import FirebaseFirestore
import FirebaseStorage
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate, MessagingDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

        // CRITICAL: Enable Firebase URL interception BEFORE Firebase configuration
        FirebaseInterceptorManager.shared.enableInterception()
        print("🔧 Early Firebase URL interception enabled")

        // Configure Firebase with enhanced error handling
        FirebaseApp.configure()
        print("✅ Firebase configured successfully")

        // Configure enhanced Firebase settings
        configureFirebaseSettings()

        // CRITICAL: Completely disable Firebase Analytics and related services to prevent QUIC issues
        Analytics.setAnalyticsCollectionEnabled(false)
        Analytics.setUserProperty("false", forName: "analytics_enabled")
        print("🔧 Firebase Analytics completely disabled to prevent QUIC issues")

        // CRITICAL: Disable Firebase In-App Messaging to prevent additional network calls
        InAppMessaging.inAppMessaging().automaticDataCollectionEnabled = false
        InAppMessaging.inAppMessaging().messageDisplaySuppressed = true
        print("🔧 Firebase In-App Messaging disabled to prevent QUIC issues")

        // Set up Firebase Messaging with enhanced configuration
        Messaging.messaging().delegate = self

        // Disable automatic swizzling to prevent warnings
        Messaging.messaging().isAutoInitEnabled = true
        print("✅ Firebase Messaging configured")

        // Set up notifications
        UNUserNotificationCenter.current().delegate = self

        // Request notification permissions with enhanced handling
        requestNotificationPermissions()

        // Register for remote notifications
        application.registerForRemoteNotifications()

        // Initialize network manager
        _ = FirebaseNetworkManager.shared

        // Initialize analytics manager
        _ = AnalyticsManager.shared
        print("✅ Analytics Manager initialized")

        // Initialize top matches manager
        _ = TopMatchesManager.shared
        print("✅ Top Matches Manager initialized")

        // Initialize online status manager
        _ = OnlineStatusManager.shared
        print("✅ Online Status Manager initialized")

        return true
    }

    // MARK: - Firebase Configuration

    private func configureFirebaseSettings() {
        // CRITICAL FIX: Use FirebaseNetworkManager for consistent configuration
        _ = FirebaseNetworkManager.shared

        // Additional AppDelegate-specific configurations
        let db = Firestore.firestore()
        let settings = FirestoreSettings()

        // Use modern cache settings instead of deprecated properties
        settings.cacheSettings = PersistentCacheSettings(sizeBytes: NSNumber(value: 100 * 1024 * 1024)) // 100MB cache

        // CRITICAL FIX: Disable HTTP/3 and QUIC at Firestore level
        settings.isSSLEnabled = true
        db.settings = settings

        // Configure Storage with conservative settings
        let storage = Storage.storage()
        storage.maxDownloadRetryTime = 30.0
        storage.maxUploadRetryTime = 30.0
        storage.maxOperationRetryTime = 30.0

        print("✅ Enhanced Firebase settings configured with QUIC disabled")
    }

    private func requestNotificationPermissions() {
        let options: UNAuthorizationOptions
        if #available(iOS 14.0, *) {
            options = [.alert, .badge, .sound, .provisional]
        } else {
            options = [.alert, .badge, .sound]
        }

        UNUserNotificationCenter.current().requestAuthorization(options: options) { granted, error in
            DispatchQueue.main.async {
                print("📱 Notification permission granted: \(granted)")
                if let error = error {
                    print("❌ Notification permission error: \(error.localizedDescription)")
                } else if granted {
                    print("✅ Notification permissions granted successfully")
                } else {
                    print("⚠️ Notification permissions denied by user")
                }
            }
        }
    }

    // MARK: - Remote Notifications

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        print("📱 Successfully registered for remote notifications")
        Messaging.messaging().apnsToken = deviceToken
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("❌ Failed to register for remote notifications: \(error.localizedDescription)")
    }

    // MARK: - UNUserNotificationCenterDelegate

    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification even when app is in foreground
        if #available(iOS 14.0, *) {
            completionHandler([.banner, .badge, .sound])
        } else {
            completionHandler([.alert, .badge, .sound])
        }
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        // Handle notification tap
        let userInfo = response.notification.request.content.userInfo
        print("📱 Notification tapped with userInfo: \(userInfo)")

        // Handle deep linking based on notification content
        handleNotificationDeepLink(userInfo: userInfo)

        completionHandler()
    }

    // MARK: - MessagingDelegate

    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        print("📱 FCM registration token: \(fcmToken ?? "nil")")

        // Send token to your server if needed
        if let token = fcmToken {
            // Store token for current user
            UserDefaults.standard.set(token, forKey: "fcm_token")
        }
    }

    // MARK: - Deep Link Handling

    private func handleNotificationDeepLink(userInfo: [AnyHashable: Any]) {
        // Handle different types of notifications
        if let chatID = userInfo["chatID"] as? String {
            // Navigate to specific chat
            NotificationCenter.default.post(
                name: NSNotification.Name("NavigateToChat"),
                object: nil,
                userInfo: ["chatID": chatID]
            )
        } else if let matchID = userInfo["matchID"] as? String {
            // Navigate to matches
            NotificationCenter.default.post(
                name: NSNotification.Name("NavigateToMatches"),
                object: nil,
                userInfo: ["matchID": matchID]
            )
        }
    }
}
