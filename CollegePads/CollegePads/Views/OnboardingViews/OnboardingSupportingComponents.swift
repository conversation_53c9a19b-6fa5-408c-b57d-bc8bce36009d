import SwiftUI
import UIKit

// MARK: - Range Slider Component
struct RangeSlider: View {
    @Binding var minValue: Int
    @Binding var maxValue: Int
    let bounds: ClosedRange<Int>
    let step: Int

    @State private var minOffset: CGFloat = 0
    @State private var maxOffset: CGFloat = 0
    @State private var sliderWidth: CGFloat = 0

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Track
                Rectangle()
                    .fill(AppTheme.textTertiary.opacity(0.3))
                    .frame(height: 4)
                    .cornerRadius(2)

                // Active track
                Rectangle()
                    .fill(AppTheme.primaryGradient)
                    .frame(width: maxOffset - minOffset, height: 4)
                    .cornerRadius(2)
                    .offset(x: minOffset)

                // Min thumb
                Circle()
                    .fill(AppTheme.primaryColor)
                    .frame(width: 24, height: 24)
                    .offset(x: minOffset - 12)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newOffset = max(0, min(value.location.x, maxOffset - 24))
                                minOffset = newOffset
                                updateMinValue(geometry: geometry)
                            }
                    )

                // Max thumb
                Circle()
                    .fill(AppTheme.primaryColor)
                    .frame(width: 24, height: 24)
                    .offset(x: maxOffset - 12)
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                let newOffset = max(minOffset + 24, min(value.location.x, geometry.size.width))
                                maxOffset = newOffset
                                updateMaxValue(geometry: geometry)
                            }
                    )
            }
            .onAppear {
                sliderWidth = geometry.size.width
                setupInitialOffsets(geometry: geometry)
            }
        }
        .frame(height: 24)
    }

    private func setupInitialOffsets(geometry: GeometryProxy) {
        let range = bounds.upperBound - bounds.lowerBound
        let minPercent = CGFloat(minValue - bounds.lowerBound) / CGFloat(range)
        let maxPercent = CGFloat(maxValue - bounds.lowerBound) / CGFloat(range)

        minOffset = minPercent * geometry.size.width
        maxOffset = maxPercent * geometry.size.width
    }

    private func updateMinValue(geometry: GeometryProxy) {
        let percent = minOffset / geometry.size.width
        let range = bounds.upperBound - bounds.lowerBound
        let newValue = bounds.lowerBound + Int(percent * CGFloat(range))
        let steppedValue = (newValue / step) * step
        minValue = max(bounds.lowerBound, min(steppedValue, maxValue - step))
    }

    private func updateMaxValue(geometry: GeometryProxy) {
        let percent = maxOffset / geometry.size.width
        let range = bounds.upperBound - bounds.lowerBound
        let newValue = bounds.lowerBound + Int(percent * CGFloat(range))
        let steppedValue = (newValue / step) * step
        maxValue = max(minValue + step, min(steppedValue, bounds.upperBound))
    }
}

// MARK: - Photo Slot Component
struct PhotoSlot: View {
    let image: UIImage?
    let index: Int
    let onTap: () -> Void
    let onRemove: () -> Void

    var body: some View {
        ZStack {
            if let image = image {
                // Image display
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 100, height: 100)
                    .clipped()
                    .cornerRadius(AppTheme.radiusMedium)
                    .overlay(
                        // Remove button
                        VStack {
                            HStack {
                                Spacer()
                                Button(action: onRemove) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.system(size: 20))
                                        .foregroundColor(.white)
                                        .background(Color.black.opacity(0.6))
                                        .clipShape(Circle())
                                }
                                .padding(4)
                            }
                            Spacer()
                        }
                    )
            } else {
                // Empty slot
                Button(action: onTap) {
                    VStack(spacing: AppTheme.spacing8) {
                        Image(systemName: index == 0 ? "camera.fill" : "plus")
                            .font(.system(size: 24))
                            .foregroundColor(AppTheme.primaryColor)

                        if index == 0 {
                            Text("Main Photo")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }
                    .frame(width: 100, height: 100)
                    .background(AppTheme.cardBackground)
                    .cornerRadius(AppTheme.radiusMedium)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .strokeBorder(AppTheme.primaryColor.opacity(0.3), lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - Onboarding Image Picker
struct OnboardingImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImages: [UIImage]
    let sourceType: UIImagePickerController.SourceType
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        picker.allowsEditing = true
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: OnboardingImagePicker

        init(_ parent: OnboardingImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                if parent.selectedImages.count < 9 {
                    parent.selectedImages.append(editedImage)
                }
            } else if let originalImage = info[.originalImage] as? UIImage {
                if parent.selectedImages.count < 9 {
                    parent.selectedImages.append(originalImage)
                }
            }

            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - Enhanced Animated Background for Onboarding
struct OnboardingAnimatedBackground: View {
    @State private var animateGradient = false
    @State private var hasCompletedInitialAnimation = false

    var body: some View {
        ZStack {
            // Base gradient
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()

            // Animated overlay - plays once then settles
            LinearGradient(
                gradient: Gradient(colors: [
                    AppTheme.primaryColor.opacity(0.1),
                    AppTheme.accentColor.opacity(0.1),
                    AppTheme.primaryColor.opacity(0.1)
                ]),
                startPoint: animateGradient ? .topLeading : .bottomTrailing,
                endPoint: animateGradient ? .bottomTrailing : .topLeading
            )
            .ignoresSafeArea()
            .animation(
                hasCompletedInitialAnimation ?
                .easeInOut(duration: 8.0) : // Slow, subtle animation after initial
                .easeInOut(duration: 2.0),   // Initial entrance animation
                value: animateGradient
            )
            .onAppear {
                // Initial entrance animation
                withAnimation(.easeInOut(duration: 2.0)) {
                    animateGradient = true
                }

                // After initial animation, switch to subtle mode
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    hasCompletedInitialAnimation = true

                    // Start very subtle, slow animation
                    withAnimation(.easeInOut(duration: 8.0)) {
                        animateGradient = false
                    }
                }
            }

            // Removed floating particles to eliminate bouncing balls
        }
    }
}

// MARK: - Static Onboarding Background (No Animation)
struct OnboardingStaticBackground: View {
    var body: some View {
        ZStack {
            // Base gradient
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()

            // Subtle static overlay
            LinearGradient(
                gradient: Gradient(colors: [
                    AppTheme.primaryColor.opacity(0.05),
                    AppTheme.accentColor.opacity(0.08),
                    AppTheme.primaryColor.opacity(0.05)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        }
    }
}

// MARK: - Subtle Onboarding Particle (Non-looping)
struct OnboardingSubtleParticle: View {
    let delay: Double
    @State private var opacity: Double = 0
    @State private var scale: CGFloat = 0.5
    @State private var yOffset: CGFloat = 0

    var body: some View {
        Circle()
            .fill(AppTheme.primaryColor.opacity(0.08))
            .frame(width: 15, height: 15)
            .scaleEffect(scale)
            .opacity(opacity)
            .offset(y: yOffset)
            .onAppear {
                // Single entrance animation
                withAnimation(.easeOut(duration: 1.5).delay(delay)) {
                    opacity = 1.0
                    scale = 1.0
                    yOffset = -20
                }

                // Settle into final position
                DispatchQueue.main.asyncAfter(deadline: .now() + delay + 1.5) {
                    withAnimation(.easeInOut(duration: 1.0)) {
                        yOffset = -10
                        opacity = 0.6
                    }
                }
            }
    }
}

// MARK: - Original Floating Particle (For other uses)
struct OnboardingFloatingParticle: View {
    let delay: Double
    @State private var isAnimating = false

    var body: some View {
        Circle()
            .fill(AppTheme.primaryColor.opacity(0.1))
            .frame(width: 20, height: 20)
            .offset(
                x: isAnimating ? CGFloat.random(in: -100...100) : 0,
                y: isAnimating ? CGFloat.random(in: -200...200) : 0
            )
            .animation(
                .easeInOut(duration: 4)
                .repeatForever(autoreverses: true)
                .delay(delay),
                value: isAnimating
            )
            .onAppear {
                isAnimating = true
            }
    }
}

// MARK: - Onboarding Progress Indicator
struct OnboardingProgressIndicator: View {
    let currentStep: Int
    let totalSteps: Int

    var body: some View {
        HStack(spacing: AppTheme.spacing8) {
            ForEach(0..<totalSteps, id: \.self) { step in
                Circle()
                    .fill(step <= currentStep ? AppTheme.primaryColor : AppTheme.textTertiary.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(step == currentStep ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: currentStep)
            }
        }
    }
}

// MARK: - Onboarding Card Container
struct OnboardingCard<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        VStack {
            content
        }
        .padding(AppTheme.spacing24)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(AppTheme.cardBackground)
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        )
        .padding(.horizontal, AppTheme.spacing20)
    }
}
