import SwiftUI

// MARK: - Budget Preferences Step View
struct BudgetPreferencesStepView: View {
    @Binding var monthlyRentMin: Int
    @Binding var monthlyRentMax: Int
    @Binding var budgetMin: Int
    @Binding var budgetMax: Int

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Header
            OnboardingHeader(
                title: "Budget Preferences",
                subtitle: "Set your budget range to find compatible roommates"
            )

            VStack(spacing: AppTheme.spacing24) {
                // Monthly rent range
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    Text("Monthly Rent Range")
                        .font(AppTheme.subtitleFont)
                        .foregroundColor(AppTheme.textPrimary)

                    Text("$\(monthlyRentMin) - $\(monthlyRentMax)")
                        .font(AppTheme.title3)
                        .foregroundStyle(AppTheme.primaryGradient)
                        .fontWeight(.bold)

                    VStack(spacing: AppTheme.spacing8) {
                        HStack {
                            Text("Min: $\(monthlyRentMin)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                            Spacer()
                            Text("Max: $\(monthlyRentMax)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        RangeSlider(
                            minValue: $monthlyRentMin,
                            maxValue: $monthlyRentMax,
                            bounds: 300...3000,
                            step: 50
                        )
                    }
                }

                // Personal budget range
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    Text("Personal Budget Range")
                        .font(AppTheme.subtitleFont)
                        .foregroundColor(AppTheme.textPrimary)

                    Text("Your share: $\(budgetMin) - $\(budgetMax)")
                        .font(AppTheme.title3)
                        .foregroundStyle(AppTheme.sexyGradient)
                        .fontWeight(.bold)

                    VStack(spacing: AppTheme.spacing8) {
                        HStack {
                            Text("Min: $\(budgetMin)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                            Spacer()
                            Text("Max: $\(budgetMax)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        RangeSlider(
                            minValue: $budgetMin,
                            maxValue: $budgetMax,
                            bounds: 200...2000,
                            step: 25
                        )
                    }
                }

                // Budget tips
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(AppTheme.accentColor)
                        Text("Budget Tips")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)
                    }

                    VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                        Text("• Include utilities, internet, and parking")
                        Text("• Consider shared expenses like groceries")
                        Text("• Leave room for unexpected costs")
                    }
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
                }
                .padding()
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.radiusMedium)
            }
        }
    }
}

// MARK: - Enhanced Lifestyle Preferences Step View
struct EnhancedLifestylePreferencesStepView: View {
    @Binding var cleanliness: Int
    @Binding var sleepSchedule: String
    @Binding var smoker: Bool
    @Binding var petFriendly: Bool

    // Enhanced lifestyle bindings to match profile setup
    @Binding var selectedPets: [String]
    @Binding var selectedDrinking: String
    @Binding var selectedSmoking: String
    @Binding var selectedCannabis: String
    @Binding var selectedWorkout: String
    @Binding var selectedDietaryPreferences: [String]
    @Binding var selectedSocialMedia: String
    @Binding var selectedSleepingHabits: String

    private let sleepSchedules = ["Early Bird", "Night Owl", "Flexible", "Varies"]

    // Options matching profile setup exactly
    private let petOptions = ["Dog","Cat","Reptile","Amphibian","Bird","Fish","Don't have but love others","Turtle","Hamster","Rabbit","Pet-free","Want a pet","Allergic to pets"]
    private let drinkingOptions = ["Not for me","Sober","Sober curious","On special occasions","Socially on weekends","Most nights"]
    private let smokingOptions = ["Non-smoker","Smoker","Smoker when drinking","Trying to quit"]
    private let cannabisOptions = ["Yes","Occasionally","Socially","Never"]
    private let workoutOptions = ["Everyday","Often","Sometimes","Never"]
    private let dietaryOptions = ["Vegan","Vegetarian","Pescatarian","Kosher","Halal","Carnivore","Omnivore","Other"]
    private let socialMediaOptions = ["Influencer status","Socially active","Off the grid","Passive scroller"]
    private let sleepingHabitsOptions = ["Early bird","Night owl","In a spectrum"]

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            ScrollView(showsIndicators: false) {
                VStack(spacing: AppTheme.spacing32) {
                    // Sexy modern header with glow effect
                    VStack(spacing: AppTheme.spacing16) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.sexyGradient)
                                .frame(width: 80, height: 80)
                                .blur(radius: 20)
                                .opacity(0.6)

                            Image(systemName: "heart.circle.fill")
                                .font(.system(size: 40, weight: .bold))
                                .foregroundColor(.white)
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            Text("Lifestyle Preferences")
                                .font(.custom("AvenirNext-Bold", size: 32))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)

                            Text("Help us match you with compatible roommates")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top, AppTheme.spacing20)

                    VStack(spacing: AppTheme.spacing24) {
                        // Cleanliness level with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "sparkles")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Cleanliness Level")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()

                                Text("Level \(cleanliness)/5")
                                    .font(.custom("AvenirNext-Bold", size: 16))
                                    .foregroundColor(.white.opacity(0.8))
                            }

                            HStack(spacing: AppTheme.spacing12) {
                                ForEach(1...5, id: \.self) { level in
                                    Button(action: {
                                        cleanliness = level
                                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                                    }) {
                                        ZStack {
                                            Circle()
                                                .fill(level <= cleanliness ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.6)))
                                                .frame(width: 50, height: 50)
                                                .overlay(
                                                    Circle()
                                                        .stroke(level <= cleanliness ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                                                )

                                            Image(systemName: level <= cleanliness ? "star.fill" : "star")
                                                .font(.system(size: 20, weight: .bold))
                                                .foregroundColor(.white)
                                        }
                                        .scaleEffect(level <= cleanliness ? 1.1 : 1.0)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: cleanliness)
                                }

                                Spacer()

                                Text(cleanlinessDescription)
                                    .font(.custom("AvenirNext-Medium", size: 14))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // Pets with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "pawprint.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Do you have any pets?")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            OnboardingMultiSelectChipView(
                                options: petOptions,
                                selectedItems: $selectedPets,
                                maxSelection: 3
                            )
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // Drinking with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "wineglass.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("How often do you drink?")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            VStack(spacing: AppTheme.spacing12) {
                                ForEach(drinkingOptions, id: \.self) { option in
                                    SexyLifestyleCard(
                                        title: option,
                                        isSelected: selectedDrinking == option,
                                        icon: "drop.fill"
                                    ) {
                                        selectedDrinking = option
                                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                                    }
                                }
                            }
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                    // Smoking
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("How often do you smoke?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                            ForEach(smokingOptions, id: \.self) { option in
                                Button(action: {
                                    selectedSmoking = option
                                    smoker = (option != "Non-smoker") // Update legacy binding
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Text(option)
                                        .font(AppTheme.caption)
                                        .foregroundColor(selectedSmoking == option ? .white : AppTheme.textPrimary)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, AppTheme.spacing8)
                                        .background(
                                            selectedSmoking == option ?
                                            AnyView(AppTheme.primaryGradient) :
                                            AnyView(AppTheme.cardBackground)
                                        )
                                        .cornerRadius(AppTheme.radiusSmall)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }

                    // Cannabis
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("Are you 420 friendly?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                            ForEach(cannabisOptions, id: \.self) { option in
                                Button(action: {
                                    selectedCannabis = option
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Text(option)
                                        .font(AppTheme.caption)
                                        .foregroundColor(selectedCannabis == option ? .white : AppTheme.textPrimary)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, AppTheme.spacing8)
                                        .background(
                                            selectedCannabis == option ?
                                            AnyView(AppTheme.primaryGradient) :
                                            AnyView(AppTheme.cardBackground)
                                        )
                                        .cornerRadius(AppTheme.radiusSmall)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }

                    // Workout
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("Do you workout?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                            ForEach(workoutOptions, id: \.self) { option in
                                Button(action: {
                                    selectedWorkout = option
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Text(option)
                                        .font(AppTheme.caption)
                                        .foregroundColor(selectedWorkout == option ? .white : AppTheme.textPrimary)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, AppTheme.spacing8)
                                        .background(
                                            selectedWorkout == option ?
                                            AnyView(AppTheme.primaryGradient) :
                                            AnyView(AppTheme.cardBackground)
                                        )
                                        .cornerRadius(AppTheme.radiusSmall)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }

                    // Dietary Preferences
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("What are your dietary preferences?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        MultiSelectChipView(
                            options: dietaryOptions,
                            selectedItems: $selectedDietaryPreferences,
                            maxSelection: 3
                        )
                    }

                    // Social Media
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("How active are you on social media?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                            ForEach(socialMediaOptions, id: \.self) { option in
                                Button(action: {
                                    selectedSocialMedia = option
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Text(option)
                                        .font(AppTheme.caption)
                                        .foregroundColor(selectedSocialMedia == option ? .white : AppTheme.textPrimary)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, AppTheme.spacing8)
                                        .background(
                                            selectedSocialMedia == option ?
                                            AnyView(AppTheme.primaryGradient) :
                                            AnyView(AppTheme.cardBackground)
                                        )
                                        .cornerRadius(AppTheme.radiusSmall)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }

                    // Sleep Schedule (Enhanced)
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("What are your sleeping habits?")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                            ForEach(sleepingHabitsOptions, id: \.self) { option in
                                Button(action: {
                                    selectedSleepingHabits = option
                                    sleepSchedule = option // Update legacy binding
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Text(option)
                                        .font(AppTheme.caption)
                                        .foregroundColor(selectedSleepingHabits == option ? .white : AppTheme.textPrimary)
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, AppTheme.spacing8)
                                        .background(
                                            selectedSleepingHabits == option ?
                                            AnyView(AppTheme.primaryGradient) :
                                            AnyView(AppTheme.cardBackground)
                                        )
                                        .cornerRadius(AppTheme.radiusSmall)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing32)
                }
            }
        }
    }

    private var cleanlinessDescription: String {
        switch cleanliness {
        case 1: return "Very Relaxed"
        case 2: return "Casual"
        case 3: return "Moderate"
        case 4: return "Neat"
        case 5: return "Very Clean"
        default: return ""
        }
    }

    private func sleepScheduleIcon(for schedule: String) -> String {
        switch schedule {
        case "Early Bird": return "sunrise.fill"
        case "Night Owl": return "moon.fill"
        case "Flexible": return "clock.fill"
        case "Varies": return "questionmark.circle.fill"
        default: return "clock.fill"
        }
    }
}

// MARK: - Profile Photos Step View
struct ProfilePhotosStepView: View {
    @Binding var selectedImages: [UIImage]
    @State private var showImagePicker = false
    @State private var imagePickerSourceType: UIImagePickerController.SourceType = .photoLibrary

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Header
            OnboardingHeader(
                title: "Add Profile Photos",
                subtitle: "Upload photos to make your profile stand out"
            )

            VStack(spacing: AppTheme.spacing20) {
                // Photo grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: AppTheme.spacing12) {
                    ForEach(0..<9, id: \.self) { index in
                        PhotoSlot(
                            image: index < selectedImages.count ? selectedImages[index] : nil,
                            index: index,
                            onTap: {
                                showImagePicker = true
                            },
                            onRemove: {
                                if index < selectedImages.count {
                                    selectedImages.remove(at: index)
                                }
                            }
                        )
                    }
                }

                // Photo tips
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Image(systemName: "camera.fill")
                            .foregroundColor(AppTheme.primaryColor)
                        Text("Photo Tips")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(AppTheme.textPrimary)
                    }

                    VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                        Text("• Use clear, recent photos of yourself")
                        Text("• Include at least one face photo")
                        Text("• Show your personality and interests")
                        Text("• Avoid group photos as your main image")
                    }
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
                }
                .padding()
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.radiusMedium)
            }
        }
        .sheet(isPresented: $showImagePicker) {
            OnboardingImagePicker(
                selectedImages: $selectedImages,
                sourceType: imagePickerSourceType
            )
        }
    }
}

// MARK: - Completion Step View
struct CompletionStepView: View {
    var isSaving: Bool = false
    var saveError: String? = nil

    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            Spacer()

            // Success animation or loading
            VStack(spacing: AppTheme.spacing24) {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 20)
                        .opacity(0.6)

                    if isSaving {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(2.0)
                    } else if saveError != nil {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 80))
                            .foregroundColor(.orange)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 80))
                            .foregroundStyle(AppTheme.sexyGradient)
                    }
                }

                VStack(spacing: AppTheme.spacing12) {
                    if isSaving {
                        Text("Saving Profile...")
                            .font(.custom("AvenirNext-Bold", size: 32))
                            .foregroundStyle(AppTheme.sexyGradient)
                            .multilineTextAlignment(.center)

                        Text("Please wait while we save your profile data to Firebase...")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppTheme.spacing16)
                    } else if let error = saveError {
                        Text("Save Error")
                            .font(.custom("AvenirNext-Bold", size: 32))
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)

                        Text("Failed to save profile: \(error)")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppTheme.spacing16)
                    } else {
                        Text("Profile Complete!")
                            .font(.custom("AvenirNext-Bold", size: 32))
                            .foregroundStyle(AppTheme.sexyGradient)
                            .multilineTextAlignment(.center)

                        Text("Your profile is now 100% complete and ready to find your perfect roommate match!")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppTheme.spacing16)
                    }
                }
            }

            // Next steps (only show when not saving and no error)
            if !isSaving && saveError == nil {
                VStack(spacing: AppTheme.spacing16) {
                NextStepItem(
                    icon: "heart.fill",
                    title: "Start Swiping",
                    description: "Discover potential roommates"
                )

                NextStepItem(
                    icon: "message.fill",
                    title: "Chat with Matches",
                    description: "Connect with compatible people"
                )

                NextStepItem(
                    icon: "house.fill",
                    title: "Find Your Home",
                    description: "Secure your perfect living situation"
                )
                }
            }

            Spacer()
        }
    }
}

// MARK: - Supporting Components

struct LifestyleToggle: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool

    var body: some View {
        HStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(isOn ? AnyShapeStyle(AppTheme.primaryGradient) : AnyShapeStyle(AppTheme.cardBackground))
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isOn ? .white : AppTheme.textSecondary)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(title)
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.textPrimary)

                Text(subtitle)
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding()
        .background(AppTheme.cardBackground)
        .cornerRadius(AppTheme.radiusMedium)
    }
}

