import SwiftUI

// MARK: - Remaining Optimized Onboarding Steps

// MARK: - Budget Preferences (Optimized)
struct OptimizedBudgetPreferencesView: View {
    @Binding var monthlyRentMin: Int
    @Binding var monthlyRentMax: Int
    @Binding var budgetMin: Int
    @Binding var budgetMax: Int
    
    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Budget Preferences",
                subtitle: "Set your budget range"
            )
            
            VStack(spacing: AppTheme.spacing24) {
                // Monthly rent
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    SectionHeader(title: "Monthly Rent Range")
                    RangeDisplay(title: "Rent", minValue: monthlyRentMin, maxValue: monthlyRentMax, prefix: "$")
                    SimpleRangeSlider(minValue: $monthlyRentMin, maxValue: $monthlyRentMax, bounds: 300...3000, step: 50)
                }
                
                // Personal budget
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    SectionHeader(title: "Personal Budget Range")
                    RangeDisplay(title: "Budget", minValue: budgetMin, maxValue: budgetMax, prefix: "$")
                    SimpleRangeSlider(minValue: $budgetMin, maxValue: $budgetMax, bounds: 200...2000, step: 25)
                }
                
                // Tips
                InfoCard(
                    icon: "lightbulb.fill",
                    title: "Budget Tips",
                    items: [
                        "Include utilities and internet",
                        "Consider shared expenses",
                        "Leave room for unexpected costs"
                    ]
                )
            }
        }
    }
}

// MARK: - Lifestyle Preferences (Optimized)
struct OptimizedLifestylePreferencesView: View {
    @Binding var cleanliness: Int
    @Binding var sleepSchedule: String
    @Binding var smoker: Bool
    @Binding var petFriendly: Bool
    
    private let sleepSchedules = ["Early Bird", "Night Owl", "Flexible", "Varies"]
    
    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Lifestyle Preferences",
                subtitle: "Help us match you with compatible roommates"
            )
            
            VStack(spacing: AppTheme.spacing24) {
                // Cleanliness
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    SectionHeader(title: "Cleanliness Level")
                    StarRating(rating: $cleanliness)
                }
                
                // Sleep schedule
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Sleep Schedule")
                    GridSelection(
                        items: sleepSchedules,
                        selectedItem: Binding(
                            get: { sleepSchedule.isEmpty ? nil : sleepSchedule },
                            set: { sleepSchedule = $0 ?? "" }
                        ),
                        columns: 2,
                        itemText: { $0 }
                    )
                }
                
                // Lifestyle toggles
                VStack(spacing: AppTheme.spacing16) {
                    IconToggle(title: "Smoker", subtitle: "Do you smoke?", icon: "smoke.fill", isOn: $smoker)
                    IconToggle(title: "Pet Friendly", subtitle: "Are you okay with pets?", icon: "pawprint.fill", isOn: $petFriendly)
                }
            }
        }
    }
}

// MARK: - Profile Photos (Optimized)
struct OptimizedProfilePhotosView: View {
    @Binding var selectedImages: [UIImage]
    @State private var showImagePicker = false
    
    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Add Profile Photos",
                subtitle: "Upload photos to make your profile stand out"
            )
            
            VStack(spacing: AppTheme.spacing20) {
                // Photo grid (3x3 = 9 photos)
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: AppTheme.spacing12) {
                    ForEach(0..<9, id: \.self) { index in
                        PhotoSlot(
                            image: index < selectedImages.count ? selectedImages[index] : nil,
                            index: index,
                            onTap: { showImagePicker = true },
                            onRemove: {
                                if index < selectedImages.count {
                                    selectedImages.remove(at: index)
                                }
                            }
                        )
                    }
                }
                
                // Tips
                InfoCard(
                    icon: "camera.fill",
                    title: "Photo Tips",
                    items: [
                        "Use clear, recent photos",
                        "Include at least one face photo",
                        "Show your personality",
                        "Avoid group photos as main image"
                    ]
                )
            }
        }
        .sheet(isPresented: $showImagePicker) {
            OnboardingImagePicker(selectedImages: $selectedImages, sourceType: .photoLibrary)
        }
    }
}

// MARK: - Completion (Optimized)
struct OptimizedCompletionView: View {
    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            Spacer()
            
            // Success animation
            VStack(spacing: AppTheme.spacing24) {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 20)
                        .opacity(0.6)
                    
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
                
                VStack(spacing: AppTheme.spacing12) {
                    Text("Profile Complete!")
                        .font(.custom("AvenirNext-Bold", size: 32))
                        .foregroundStyle(AppTheme.sexyGradient)
                    
                    Text("Ready to find your perfect roommate!")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Next steps
            VStack(spacing: AppTheme.spacing16) {
                NextStepItem(icon: "heart.fill", title: "Start Swiping", description: "Discover potential roommates")
                NextStepItem(icon: "message.fill", title: "Chat with Matches", description: "Connect with compatible people")
                NextStepItem(icon: "house.fill", title: "Find Your Home", description: "Secure your perfect living situation")
            }
            
            Spacer()
        }
    }
}

// MARK: - Welcome Step (Optimized)
struct OptimizedWelcomeView: View {
    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            Spacer()
            
            // App branding
            VStack(spacing: AppTheme.spacing24) {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 20)
                        .opacity(0.6)
                    
                    AppIcon.splash()
                        .frame(width: 80, height: 80)
                }
                
                VStack(spacing: AppTheme.spacing12) {
                    Text("Welcome to CollegePads!")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)
                    
                    Text("Let's set up your profile to find the perfect roommate")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Features
            VStack(spacing: AppTheme.spacing16) {
                FeatureHighlight(
                    icon: "person.2.fill",
                    title: "Smart Matching",
                    description: "Find compatible roommates based on your preferences"
                )
                
                FeatureHighlight(
                    icon: "message.fill",
                    title: "Safe Messaging",
                    description: "Chat securely with potential roommates"
                )
                
                FeatureHighlight(
                    icon: "house.fill",
                    title: "Perfect Housing",
                    description: "Discover the ideal living situation for you"
                )
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Component
struct NextStepItem: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.primaryGradient)
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            
            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(title)
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.textPrimary)
                
                Text(description)
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing20)
    }
}
