import SwiftUI
import FirebaseFirestore

struct DedicatedTopMatchesView: View {
    @StateObject private var topMatchesManager = TopMatchesManager.shared
    @StateObject private var profileVM = ProfileViewModel.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showPremiumUpgrade = false
    @State private var selectedMatch: UserModel?
    @State private var animateCards = false
    @State private var showPaywall = false

    var body: some View {
        ZStack {
            // Premium Background
            premiumBackground

            // Content area - only show for premium users, completely hide for non-premium
            if profileVM.userProfile?.isPremium == true {
                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Premium Header
                        premiumHeaderView

                        // Content - full access for premium users
                        premiumContentView

                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            } else {
                // Show placeholder content that gets completely covered by paywall
                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Empty placeholder to maintain layout structure
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: 100)

                        Rectangle()
                            .fill(Color.clear)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)

                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            }

            // Paywall overlay for non-premium users - covers entire screen
            if profileVM.userProfile?.isPremium != true {
                PremiumPaywallOverlay(
                    title: "Premium Top Matches",
                    subtitle: "Get access to daily curated matches with 90%+ compatibility",
                    onUpgrade: {
                        showPremiumUpgrade = true
                    },
                    onDismiss: {
                        dismiss()
                    }
                )
            }
        }
        .navigationTitle("Top Matches")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            loadTopMatches()
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3)) {
                animateCards = true
            }
        }
        .sheet(item: $selectedMatch) { match in
            TopMatchDetailView(match: match)
        }
        .sheet(isPresented: $showPremiumUpgrade) {
            PremiumUpgradeView()
        }
    }

    // MARK: - Premium Background
    private var premiumBackground: some View {
        ZStack {
            // Base gradient
            AppTheme.backgroundGradient
                .ignoresSafeArea()

            // Premium overlay
            LinearGradient(
                colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    Color.clear,
                    Color.yellow.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        }
    }

    // MARK: - Premium Header
    private var premiumHeaderView: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Crown Icon with Animation
            Image(systemName: "crown.fill")
                .font(.system(size: 48, weight: .bold))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.yellow, Color.orange, Color.yellow],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
                .scaleEffect(animateCards ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)

            VStack(spacing: AppTheme.spacing8) {
                Text("Premium Top Matches")
                    .font(.system(size: 28, weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Your daily selection of highest compatibility matches")
                    .font(AppTheme.subheadline)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(AppTheme.spacing20)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
        .shadow(color: Color.yellow.opacity(0.2), radius: 16, x: 0, y: 8)
    }

    // MARK: - Premium Content
    private var premiumContentView: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Usage Stats
            usageStatsView

            // Top Matches Grid
            if topMatchesManager.isLoading {
                loadingView
            } else if topMatchesManager.topMatches.isEmpty {
                emptyStateView
            } else {
                topMatchesGridView
            }
        }
    }

    // MARK: - Usage Stats
    private var usageStatsView: some View {
        HStack(spacing: AppTheme.spacing16) {
            StatCard(
                title: "Views Left",
                value: "\(topMatchesManager.maxDailyViews - topMatchesManager.viewedCount)",
                icon: "eye.fill",
                color: AppTheme.primaryColor,
                isPremium: true
            )

            StatCard(
                title: "Likes Left",
                value: "\(topMatchesManager.maxDailyLikes - topMatchesManager.likedCount)",
                icon: "heart.fill",
                color: .red,
                isPremium: true
            )
        }
    }

    // MARK: - Top Matches Grid
    private var topMatchesGridView: some View {
        VStack(spacing: AppTheme.spacing16) {
            HStack {
                Text("Today's Top Matches")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()

                Text("Resets at midnight")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textTertiary)
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: AppTheme.spacing12), count: 2), spacing: AppTheme.spacing16) {
                ForEach(Array(topMatchesManager.topMatches.enumerated()), id: \.offset) { index, match in
                    TopMatchCard(
                        match: match,
                        index: index,
                        canView: topMatchesManager.canViewProfile(at: index),
                        canLike: topMatchesManager.canLikeProfile(),
                        onTap: {
                            handleCardTap(match: match, index: index)
                        },
                        onLike: {
                            handleCardLike(match: match)
                        }
                    )
                    .scaleEffect(animateCards ? 1.0 : 0.8)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                        .delay(Double(index) * 0.1),
                        value: animateCards
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
        .shadow(color: Color.yellow.opacity(0.2), radius: 16, x: 0, y: 8)
    }

    // MARK: - Paywall View
    private var paywallView: some View {
        VStack(spacing: AppTheme.spacing24) {
            VStack(spacing: AppTheme.spacing16) {
                Image(systemName: "lock.fill")
                    .font(.system(size: 64, weight: .bold))
                    .foregroundColor(AppTheme.textTertiary)

                Text("Premium Feature")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Unlock daily top matches with premium subscription")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: { showPremiumUpgrade = true }) {
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 18, weight: .bold))

                    Text("Upgrade to Premium")
                        .font(.system(size: 18, weight: .bold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing24)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    LinearGradient(
                        colors: [Color.yellow, Color.orange],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.radiusLarge)
                .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
            }
        }
        .padding(AppTheme.spacing32)
        .background(AppTheme.modernCardGradient)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .stroke(AppTheme.textTertiary.opacity(0.2), lineWidth: 1)
        )
        .shadow(color: .black.opacity(0.1), radius: 16, x: 0, y: 8)
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(Color.yellow)

            Text("Loading your top matches...")
                .font(AppTheme.subheadline)
                .foregroundColor(AppTheme.textSecondary)
        }
        .padding(AppTheme.spacing32)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "heart.slash")
                .font(.system(size: 48, weight: .bold))
                .foregroundColor(AppTheme.textTertiary)

            Text("No Top Matches Yet")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(AppTheme.textPrimary)

            Text("Check back tomorrow for fresh matches!")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(AppTheme.spacing32)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
    }

    // MARK: - Premium Styling Components
    private var premiumCardBackground: some View {
        ZStack {
            AppTheme.modernCardGradient

            LinearGradient(
                colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.yellow.opacity(0.6),
                        Color.orange.opacity(0.4),
                        Color.yellow.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 2
            )
    }

    // MARK: - Actions
    private func loadTopMatches() {
        guard let userID = profileVM.userProfile?.id else { return }

        Task {
            await topMatchesManager.loadTopMatches(for: userID)
        }
    }

    private func handleCardTap(match: UserModel, index: Int) {
        topMatchesManager.viewProfile(
            at: index,
            userID: profileVM.userProfile?.id ?? "",
            viewedUserID: match.id ?? ""
        )
        selectedMatch = match
    }

    private func handleCardLike(match: UserModel) {
        if topMatchesManager.likeProfile(userID: profileVM.userProfile?.id ?? "") {
            // Create premium match
            createPremiumMatch(with: match)
            HapticFeedbackManager.shared.generateImpact(style: .medium)
        } else {
            showPremiumUpgrade = true
        }
    }

    private func createPremiumMatch(with user: UserModel) {
        Task {
            do {
                try await MatchingService.shared.createMatch(
                    user1ID: profileVM.userProfile?.id ?? "",
                    user2ID: user.id ?? "",
                    isPremiumMatch: true
                )

                AnalyticsManager.shared.trackEvent("premium_match_created_dedicated", parameters: [
                    "match_user_id": user.id ?? "",
                    "compatibility_score": user.compatibilityScore ?? 0.0
                ])

            } catch {
                print("❌ Failed to create premium match: \(error)")
            }
        }
    }
}

// MARK: - Premium Upgrade View
struct PremiumUpgradeView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var profileVM = ProfileViewModel.shared
    @State private var animateFeatures = false
    @State private var selectedPlan: PremiumPlan = .lifetime

    enum PremiumPlan: String, CaseIterable {
        case lifetime = "Lifetime"

        var price: String {
            return "$4.99"
        }

        var savings: String? {
            return "One-time payment"
        }

        var description: String {
            return "Unlock all features forever"
        }
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Premium background
                AppTheme.backgroundGradient
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: AppTheme.spacing20) {
                        // Header
                        premiumHeaderView

                        // Features list
                        premiumFeaturesView

                        // Pricing plans
                        pricingPlansView

                        // Purchase button
                        purchaseButtonView

                        // Terms and restore
                        termsAndRestoreView

                        Spacer(minLength: AppTheme.spacing16)
                    }
                    .padding(AppTheme.spacing16)
                }
            }
            .navigationTitle("Premium")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppTheme.textPrimary)
                }
            }
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3)) {
                animateFeatures = true
            }
        }
    }

    // MARK: - Premium Header
    private var premiumHeaderView: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Crown with glow - more compact
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.yellow.opacity(0.4),
                                Color.orange.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 25,
                            endRadius: 60
                        )
                    )
                    .frame(width: 120, height: 120)

                Image(systemName: "crown.fill")
                    .font(.system(size: 48, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.yellow, Color.orange, Color.yellow],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: Color.yellow.opacity(0.6), radius: 8, x: 0, y: 4)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("Unlock CollegePads Premium")
                    .font(.system(size: 26, weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("We know you won't always need to find a roommate. That's why we offer a one-time payment to unlock all premium features for life.")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(4)
            }
        }
    }

    // MARK: - Premium Features
    private var premiumFeaturesView: some View {
        VStack(spacing: AppTheme.spacing12) {
            ForEach(Array(premiumFeatures.enumerated()), id: \.offset) { index, feature in
                premiumFeatureRow(
                    icon: feature.icon,
                    title: feature.title,
                    description: feature.description,
                    index: index
                )
                .scaleEffect(animateFeatures ? 1.0 : 0.8)
                .opacity(animateFeatures ? 1.0 : 0.0)
                .animation(
                    .spring(response: 0.6, dampingFraction: 0.8)
                    .delay(Double(index) * 0.1),
                    value: animateFeatures
                )
            }
        }
    }

    private func premiumFeatureRow(icon: String, title: String, description: String, index: Int) -> some View {
        HStack(spacing: AppTheme.spacing12) {
            // Icon - more compact
            Image(systemName: icon)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.yellow)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(Color.yellow.opacity(0.2))
                        .overlay(
                            Circle()
                                .stroke(Color.yellow.opacity(0.4), lineWidth: 1)
                        )
                )

            // Content - more compact
            VStack(alignment: .leading, spacing: AppTheme.spacing2) {
                Text(title)
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text(description)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
        .padding(AppTheme.spacing12)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Pricing Plans
    private var pricingPlansView: some View {
        VStack(spacing: AppTheme.spacing12) {
            Text("Simple, One-Time Payment")
                .font(.system(size: 20, weight: .bold, design: .rounded))
                .foregroundColor(AppTheme.textPrimary)

            pricingPlanCard(plan: .lifetime)
        }
    }

    private func pricingPlanCard(plan: PremiumPlan) -> some View {
        let isSelected = selectedPlan == plan

        return Button(action: {
            selectedPlan = plan
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            VStack(spacing: AppTheme.spacing12) {
                // One-time payment badge
                Text("ONE-TIME PAYMENT")
                    .font(.system(size: 11, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, AppTheme.spacing12)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(Color.green)
                    .cornerRadius(AppTheme.radiusSmall)

                // Plan name
                Text("Lifetime Premium")
                    .font(.system(size: 20, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                // Price
                VStack(spacing: AppTheme.spacing2) {
                    Text("$4.99")
                        .font(.system(size: 32, weight: .black, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.yellow, Color.orange],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Pay once, use forever")
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppTheme.textSecondary)
                }

                // Description
                Text("No monthly fees, no subscriptions")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .padding(AppTheme.spacing16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(
                                isSelected ? Color.yellow : Color.gray.opacity(0.3),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
    }

    // MARK: - Purchase Button
    private var purchaseButtonView: some View {
        Button(action: {
            purchasePremium()
        }) {
            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: "crown.fill")
                    .font(.system(size: 18, weight: .bold))

                Text("Get Lifetime Premium - $4.99")
                    .font(.system(size: 18, weight: .bold, design: .rounded))
            }
            .foregroundColor(.white)
            .padding(.horizontal, AppTheme.spacing24)
            .padding(.vertical, AppTheme.spacing16)
            .background(
                LinearGradient(
                    colors: [Color.yellow, Color.orange],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(AppTheme.radiusLarge)
            .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
        }
    }

    // MARK: - Terms and Restore
    private var termsAndRestoreView: some View {
        VStack(spacing: AppTheme.spacing8) {
            Button("Restore Purchases") {
                restorePurchases()
            }
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(AppTheme.primaryColor)

            Text("Terms of Service • Privacy Policy")
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(AppTheme.textTertiary)
        }
    }

    // MARK: - Premium Features Data
    private var premiumFeatures: [(icon: String, title: String, description: String)] {
        [
            ("infinity", "Unlimited Daily Swipes", "No more waiting - swipe as much as you want"),
            ("crown.fill", "Premium Top Matches", "8 daily curated matches with 90%+ compatibility"),
            ("star.fill", "Premium Super Likes", "3 weekly super likes to stand out and get noticed"),
            ("magnifyingglass.circle.fill", "Advanced User Search", "Find users by name, major, interests"),
            ("eye.fill", "Profile Views", "See who viewed your profile and connect with them"),
            ("heart.fill", "Unlimited Likes", "Like as many profiles as you want, forever"),
            ("sparkles", "Lifetime Access", "Pay once, use forever - no subscriptions")
        ]
    }

    // MARK: - Actions
    private func purchasePremium() {
        // TODO: Implement actual purchase logic
        print("🎯 Purchase Premium: \(selectedPlan.rawValue)")

        // For now, simulate successful purchase
        simulateSuccessfulPurchase()
    }

    private func restorePurchases() {
        // TODO: Implement restore purchases
        print("🔄 Restore Purchases")
    }

    private func simulateSuccessfulPurchase() {
        // Simulate Firebase update
        Task {
            // Update user's premium status in Firebase
            // This would normally be done through your payment processor
            await updatePremiumStatus(true)

            DispatchQueue.main.async {
                dismiss()
            }
        }
    }

    private func updatePremiumStatus(_ isPremium: Bool) async {
        guard let userID = profileVM.userProfile?.id else {
            print("❌ No user ID available for premium update")
            return
        }

        print("🎯 Updating premium status to: \(isPremium) for user: \(userID)")

        do {
            // Update Firebase user document
            let db = Firestore.firestore()
            let updateData: [String: Any] = [
                "isPremium": isPremium,
                "premiumSince": isPremium ? FieldValue.serverTimestamp() : FieldValue.delete(),
                "premiumPlan": isPremium ? "lifetime" : FieldValue.delete()
            ]

            try await db.collection("users").document(userID).updateData(updateData)
            print("✅ Successfully updated premium status in Firebase")

            // Update local profile
            DispatchQueue.main.async {
                self.profileVM.userProfile?.isPremium = isPremium
                if isPremium {
                    self.profileVM.userProfile?.premiumSince = Date()
                    self.profileVM.userProfile?.premiumPlan = "lifetime"
                } else {
                    self.profileVM.userProfile?.premiumSince = nil
                    self.profileVM.userProfile?.premiumPlan = nil
                }
            }

        } catch {
            print("❌ Failed to update premium status in Firebase: \(error)")
        }
    }
}

// MARK: - Preview
struct DedicatedTopMatchesView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DedicatedTopMatchesView()
        }
    }
}
