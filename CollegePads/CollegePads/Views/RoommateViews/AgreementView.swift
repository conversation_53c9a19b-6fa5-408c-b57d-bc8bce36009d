import SwiftUI

struct AgreementView: View {
    let matchID: String
    let userA: String
    let userB: String
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTemplate: AgreementTemplate = .basic
    @State private var customSections: [AgreementSection] = []
    @State private var isCreating: Bool = false
    @State private var showTemplateSelector: Bool = false
    @State private var agreementTitle: String = ""
    @State private var currentStep: AgreementStep = .template
    @State private var userASignature: String = ""
    @State private var userBSignature: String = ""
    @State private var showSignatureView: Bool = false
    @StateObject private var agreementManager = AgreementManager.shared
    
    var body: some View {
        NavigationView {
            ZStack {
                // Beautiful background
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Header
                        headerView

                        // Progress indicator
                        progressIndicatorView

                        // Step content
                        stepContentView

                        // Navigation buttons
                        navigationButtonsView

                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            }
            .navigationTitle("Roommate Agreement")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "doc.text.fill")
                .font(.system(size: 60, weight: .medium))
                .foregroundStyle(AppTheme.sexyGradient)
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
            
            Text("Create Roommate Agreement")
                .font(.custom("AvenirNext-Bold", size: 24))
                .foregroundStyle(AppTheme.sexyGradient)
                .multilineTextAlignment(.center)
            
            Text("Set clear expectations for a harmonious living situation")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var templateSelectorView: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("Choose Template")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: AppTheme.spacing12) {
                ForEach(AgreementTemplate.allCases, id: \.self) { template in
                    TemplateCard(
                        template: template,
                        isSelected: selectedTemplate == template,
                        onSelect: { selectedTemplate = template }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(AppTheme.glassEffect)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var agreementPreviewView: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("Agreement Preview")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                ForEach(selectedTemplate.sections, id: \.title) { section in
                    AgreementSectionView(section: section)
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var actionButtonsView: some View {
        VStack(spacing: AppTheme.spacing12) {
            Button(action: createAgreement) {
                HStack {
                    if isCreating {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "doc.badge.plus")
                            .font(.system(size: 18, weight: .medium))
                    }
                    
                    Text(isCreating ? "Creating..." : "Create Agreement")
                        .font(.custom("AvenirNext-Bold", size: 18))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.sexyGradient)
                )
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .disabled(isCreating)
            
            Button(action: {
                // TODO: Customize agreement
            }) {
                Text("Customize Agreement")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.primaryColor)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, AppTheme.spacing12)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(AppTheme.primaryColor, lineWidth: 2)
                    )
            }
        }
    }
    
    // MARK: - New View Components

    private var progressIndicatorView: some View {
        HStack {
            ForEach(Array(AgreementStep.allCases.enumerated()), id: \.offset) { index, step in
                HStack {
                    Circle()
                        .fill(index <= AgreementStep.allCases.firstIndex(of: currentStep)! ? AppTheme.primaryColor : Color.gray.opacity(0.3))
                        .frame(width: 12, height: 12)

                    if index < AgreementStep.allCases.count - 1 {
                        Rectangle()
                            .fill(index < AgreementStep.allCases.firstIndex(of: currentStep)! ? AppTheme.primaryColor : Color.gray.opacity(0.3))
                            .frame(height: 2)
                    }
                }
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
    }

    @ViewBuilder
    private var stepContentView: some View {
        switch currentStep {
        case .template:
            templateSelectorView
        case .customize:
            customizationView
        case .review:
            agreementPreviewView
        case .sign:
            signatureView
        }
    }

    private var customizationView: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing20) {
            Text("Customize Your Agreement")
                .font(.custom("AvenirNext-Bold", size: 24))
                .foregroundStyle(AppTheme.sexyGradient)

            Text("Add or modify sections to fit your specific needs")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(AppTheme.textSecondary)

            // Agreement title
            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                Text("Agreement Title")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(AppTheme.textPrimary)

                TextField("Enter agreement title", text: $agreementTitle)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }

            // Editable sections
            LazyVStack(spacing: AppTheme.spacing16) {
                ForEach(Array(selectedTemplate.sections.enumerated()), id: \.offset) { index, section in
                    EditableSectionView(
                        section: section,
                        onUpdate: { updatedSection in
                            // Update section logic
                        },
                        onDelete: {
                            // Delete section logic
                        }
                    )
                }
            }

            // Add section button
            Button(action: {
                // Add new section
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add Custom Section")
                }
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(AppTheme.primaryColor)
                .padding(.vertical, AppTheme.spacing12)
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(AppTheme.glassEffect)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var signatureView: some View {
        VStack(spacing: AppTheme.spacing24) {
            Text("Digital Signatures")
                .font(.custom("AvenirNext-Bold", size: 24))
                .foregroundStyle(AppTheme.sexyGradient)

            Text("Both parties must sign to finalize the agreement")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)

            VStack(spacing: AppTheme.spacing20) {
                SignatureFieldView(
                    title: "User A Signature",
                    signature: $userASignature,
                    isSigned: !userASignature.isEmpty
                )

                SignatureFieldView(
                    title: "User B Signature",
                    signature: $userBSignature,
                    isSigned: !userBSignature.isEmpty
                )
            }

            if !userASignature.isEmpty && !userBSignature.isEmpty {
                Text("✅ Agreement is ready to be finalized")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.green)
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(AppTheme.glassEffect)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var navigationButtonsView: some View {
        HStack(spacing: AppTheme.spacing16) {
            if currentStep != .template {
                Button(action: goToPreviousStep) {
                    Text("Back")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.primaryColor)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, AppTheme.spacing12)
                        .background(
                            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                                .stroke(AppTheme.primaryColor, lineWidth: 2)
                        )
                }
            }

            Button(action: handleNextStep) {
                HStack {
                    if isCreating {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    }

                    Text(nextButtonTitle)
                        .font(.custom("AvenirNext-Bold", size: 16))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(canProceed ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.gray))
                )
                .shadow(color: canProceed ? AppTheme.primaryColor.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
            }
            .disabled(!canProceed || isCreating)
        }
    }

    // MARK: - Computed Properties

    private var nextButtonTitle: String {
        switch currentStep {
        case .template: return "Next"
        case .customize: return "Review"
        case .review: return "Sign"
        case .sign: return isCreating ? "Creating..." : "Create Agreement"
        }
    }

    private var canProceed: Bool {
        switch currentStep {
        case .template: return true
        case .customize: return !agreementTitle.isEmpty
        case .review: return true
        case .sign: return !userASignature.isEmpty && !userBSignature.isEmpty
        }
    }

    // MARK: - Actions

    private func goToPreviousStep() {
        let allSteps = AgreementStep.allCases
        if let currentIndex = allSteps.firstIndex(of: currentStep), currentIndex > 0 {
            currentStep = allSteps[currentIndex - 1]
        }
    }

    private func handleNextStep() {
        let allSteps = AgreementStep.allCases
        if let currentIndex = allSteps.firstIndex(of: currentStep) {
            if currentIndex < allSteps.count - 1 {
                currentStep = allSteps[currentIndex + 1]
            } else {
                createAgreement()
            }
        }
    }

    private func createAgreement() {
        isCreating = true

        Task {
            do {
                let agreementID = try await agreementManager.createAgreement(
                    matchID: matchID,
                    userA: userA,
                    userB: userB,
                    template: selectedTemplate,
                    customSections: customSections,
                    title: agreementTitle
                )

                print("✅ Agreement created successfully: \(agreementID)")

                DispatchQueue.main.async {
                    self.isCreating = false
                    self.dismiss()
                }

            } catch {
                print("❌ Failed to create agreement: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isCreating = false
                    // Show error to user
                }
            }
        }
    }
}

// MARK: - Supporting Types
enum AgreementTemplate: String, CaseIterable {
    case basic = "basic"
    case detailed = "detailed"
    case custom = "custom"
    
    var title: String {
        switch self {
        case .basic: return "Basic"
        case .detailed: return "Detailed"
        case .custom: return "Custom"
        }
    }
    
    var description: String {
        switch self {
        case .basic: return "Essential rules and responsibilities"
        case .detailed: return "Comprehensive agreement with all aspects"
        case .custom: return "Build your own from scratch"
        }
    }
    
    var sections: [AgreementSection] {
        switch self {
        case .basic:
            return [
                AgreementSection(title: "Rent & Utilities", content: "Split rent and utilities equally"),
                AgreementSection(title: "Chores", content: "Rotate weekly cleaning duties"),
                AgreementSection(title: "Guests", content: "Notify 24 hours in advance")
            ]
        case .detailed:
            return [
                AgreementSection(title: "Rent & Utilities", content: "Split rent and utilities equally"),
                AgreementSection(title: "Chores", content: "Rotate weekly cleaning duties"),
                AgreementSection(title: "Guests", content: "Notify 24 hours in advance"),
                AgreementSection(title: "Quiet Hours", content: "10 PM - 8 AM on weekdays"),
                AgreementSection(title: "Shared Spaces", content: "Keep common areas clean"),
                AgreementSection(title: "Personal Items", content: "Ask before using others' belongings")
            ]
        case .custom:
            return []
        }
    }
}

struct AgreementSection: Codable, Hashable, Equatable {
    let title: String
    let content: String
}

struct TemplateCard: View {
    let template: AgreementTemplate
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: AppTheme.spacing8) {
                Text(template.title)
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(isSelected ? .white : AppTheme.textPrimary)
                
                Text(template.description)
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(isSelected ? .white.opacity(0.9) : AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(AppTheme.spacing12)
            .frame(maxWidth: .infinity, minHeight: 80)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color(.systemGray6)))
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .stroke(isSelected ? Color.clear : Color.gray.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AgreementSectionView: View {
    let section: AgreementSection
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(section.title)
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(AppTheme.textPrimary)
            
            Text(section.content)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)
        }
        .padding(.vertical, AppTheme.spacing8)
    }
}

// MARK: - Supporting Views

struct EditableSectionView: View {
    let section: AgreementSection
    let onUpdate: (AgreementSection) -> Void
    let onDelete: () -> Void

    @State private var isEditing: Bool = false
    @State private var editedTitle: String = ""
    @State private var editedContent: String = ""

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            HStack {
                if isEditing {
                    TextField("Section Title", text: $editedTitle)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                } else {
                    Text(section.title)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(AppTheme.textPrimary)
                }

                Spacer()

                HStack(spacing: AppTheme.spacing8) {
                    Button(action: {
                        if isEditing {
                            // Save changes
                            let updatedSection = AgreementSection(title: editedTitle, content: editedContent)
                            onUpdate(updatedSection)
                        } else {
                            // Start editing
                            editedTitle = section.title
                            editedContent = section.content
                        }
                        isEditing.toggle()
                    }) {
                        Image(systemName: isEditing ? "checkmark.circle.fill" : "pencil.circle.fill")
                            .foregroundColor(isEditing ? .green : AppTheme.primaryColor)
                    }

                    Button(action: onDelete) {
                        Image(systemName: "trash.circle.fill")
                            .foregroundColor(.red)
                    }
                }
            }

            if isEditing {
                TextField("Section Content", text: $editedContent, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(minHeight: 60)
            } else {
                Text(section.content)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct SignatureFieldView: View {
    let title: String
    @Binding var signature: String
    let isSigned: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(title)
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(AppTheme.textPrimary)

            HStack {
                TextField("Enter your full name", text: $signature)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(isSigned)

                if isSigned {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.system(size: 20))
                }
            }

            if isSigned {
                Text("Signed on \(Date().formatted(date: .abbreviated, time: .shortened))")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.green)
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(isSigned ? Color.green.opacity(0.1) : Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(isSigned ? Color.green : Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct AgreementView_Previews: PreviewProvider {
    static var previews: some View {
        AgreementView(matchID: "test", userA: "userA", userB: "userB")
    }
}
