import SwiftUI
import FirebaseAuth

struct HomeView: View {
    @StateObject private var profileVM = ProfileViewModel.shared
    @StateObject private var matchingVM = MatchingViewModel()
    @StateObject private var profileViewVM = ProfileViewTrackingViewModel()
    @StateObject private var swipeAnalyticsVM = SwipeAnalyticsViewModel()

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppTheme.spacing24) {
                    // Header Section
                    headerSection

                    // Profile Section
                    profileSection

                    // Stats Grid
                    statsGrid

                    Spacer(minLength: AppTheme.spacing32)
                }
                .padding(AppTheme.spacing20)
            }
            .background(AppTheme.backgroundGradient.ignoresSafeArea())
            .onAppear {
                // OPTIMIZATION: Removed automatic MatchingViewModel refresh
                // MatchingViewModel will only refresh through explicit user actions:
                // 1. Manual refresh button in SwipeDeckView
                // 2. Filter changes in AdvancedFilterView
                // 3. Initial load when SwipeDeckView first appears
                profileViewVM.refresh()

                // Load accurate swipe analytics from Firebase
                swipeAnalyticsVM.loadSwipeAnalytics()

                // Sync MatchingViewModel stats with Firebase for accuracy
                matchingVM.syncStatsWithFirebase()
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink(destination: SettingsView().environmentObject(AuthViewModel())) {
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            AppTheme.primaryColor.opacity(0.2),
                                            AppTheme.accentColor.opacity(0.1)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .frame(width: 36, height: 36)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                                .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)

                            Image(systemName: "gearshape.fill")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                        .scaleEffect(1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: true)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }

    // MARK: - Ultra-Modern Header Section
    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing12) {
            // Main welcome text with gradient
            Text("Welcome to CollegePads!")
                .font(.system(size: 32, weight: .black, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .multilineTextAlignment(.center)
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)

            if let profile = profileVM.userProfile {
                HStack(spacing: 8) {
                    Text("Hi, \(profile.firstName ?? "there")!")
                        .font(.system(size: 20, weight: .semibold, design: .rounded))
                        .foregroundColor(AppTheme.textSecondary)

                    // Animated wave emoji
                    Text("👋")
                        .font(.system(size: 24))
                        .scaleEffect(1.2)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: true)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(.ultraThinMaterial)
                        .overlay(
                            Capsule()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.top, AppTheme.spacing8)
    }

    // MARK: - Profile Section
    private var profileSection: some View {
        EnhancedCard(shadowStyle: .medium) {
            VStack(spacing: AppTheme.spacing16) {
                if let profile = profileVM.userProfile {
                    let completion = ProfileCompletionCalculator.calculateCompletion(for: profile)

                    NavigationLink(destination: MyProfileView()) {
                        HStack(spacing: AppTheme.spacing16) {
                            // Enhanced Profile Ring
                            ProfileCompletionRingView(
                                completion: completion,
                                imageUrl: profile.profileImageUrl
                            )
                            .frame(width: 80, height: 80)

                            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                                Text("Profile Completion")
                                    .font(AppTheme.headline)
                                    .foregroundColor(AppTheme.textPrimary)

                                Text("\(Int(completion))% Complete")
                                    .font(AppTheme.title3)
                                    .foregroundColor(AppTheme.primaryColor)

                                // Modern progress bar with gradient
                                ZStack(alignment: .leading) {
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(AppTheme.surfaceSecondary.opacity(0.3))
                                        .frame(height: 8)

                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(
                                            LinearGradient(
                                                colors: [AppTheme.primaryColor, AppTheme.accentColor],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: CGFloat(completion / 100) * 200, height: 8)
                                        .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 4, x: 0, y: 2)
                                        .animation(.spring(response: 0.8, dampingFraction: 0.7), value: completion)
                                }
                                .frame(width: 200)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(AppTheme.textTertiary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    HStack {
                        ProfileImageView(imageUrl: nil, size: 80)

                        VStack(alignment: .leading) {
                            Text("Complete your profile")
                                .font(AppTheme.headline)
                                .foregroundColor(AppTheme.textPrimary)

                            Text("Get started to find matches")
                                .font(AppTheme.body)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()
                    }
                }
            }
        }
    }

    // MARK: - Stats Grid (2x3 Layout)
    private var statsGrid: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Row 1 - Keep existing 2-grid layout
            HStack(spacing: AppTheme.spacing16) {
                StatCard(
                    title: "Potential Matches",
                    value: potentialMatchesText,
                    icon: "heart.fill",
                    color: .red
                )

                StatCard(
                    title: "Match Rate",
                    value: accurateMatchRateText,
                    icon: "chart.line.uptrend.xyaxis",
                    color: AppTheme.accentColor
                )
            }

            // Row 2 - Expanded cards (2 horizontal spaces each)
            VStack(spacing: AppTheme.spacing16) {
                // Top Matches Premium Feature (full width)
                topMatchesNavigationCard

                // Profile Views StatCard (full width with premium styling)
                profileViewsExpandedCard
            }
        }
    }



    // MARK: - Top Matches Navigation Card (Premium Feature)
    private var topMatchesNavigationCard: some View {
        NavigationLink(destination: DedicatedTopMatchesView()) {
            PremiumStatCard(
                title: "Premium Top Matches",
                value: topMatchesDisplayValue,
                subtitle: "Daily curated matches",
                icon: "crown.fill",
                isPremium: true
            )
            .onTapGesture {
                HapticFeedbackManager.shared.generateImpact(style: .medium)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Profile Views Expanded Card (Premium Feature)
    private var profileViewsExpandedCard: some View {
        NavigationLink(destination: ProfileViewHistoryView()) {
            PremiumStatCard(
                title: "Profile Views",
                value: profileViewVM.homeSummary.totalViewsText,
                subtitle: "See who viewed you",
                icon: "eye.fill",
                isPremium: true
            )
            .onTapGesture {
                HapticFeedbackManager.shared.generateImpact(style: .light)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Top Matches Display Value
    private var topMatchesDisplayValue: String {
        if profileVM.userProfile?.isPremium == true {
            return "8 Daily"
        } else {
            return "Upgrade"
        }
    }



    // MARK: - Computed Properties for Stats

    private var matchRateText: String {
        if matchingVM.rightSwipesCount > 0 {
            let rate = (Double(matchingVM.mutualMatchesCount) / Double(matchingVM.rightSwipesCount)) * 100
            return String(format: "%.0f%%", rate)
        } else {
            return "0%"
        }
    }

    // Improved match rate using Firebase analytics data
    private var accurateMatchRateText: String {
        let totalRightSwipes = swipeAnalyticsVM.totalRightSwipes
        let totalMatches = swipeAnalyticsVM.totalMutualMatches

        if totalRightSwipes > 0 {
            let rate = (Double(totalMatches) / Double(totalRightSwipes)) * 100
            return String(format: "%.0f%%", rate)
        } else {
            return "0%"
        }
    }

    // Better potential matches display
    private var potentialMatchesText: String {
        let currentLoaded = matchingVM.potentialMatches.count

        // If we have loaded matches, show the count
        if currentLoaded > 0 {
            return "\(currentLoaded)+"
        } else if matchingVM.isLoading {
            return "..."
        } else {
            return "0"
        }
    }




}

/// A reusable ring view that displays the user’s image in the center
/// and a circular progress stroke around it representing profile completion (0–100).
struct ProfileCompletionRingView: View {
    let completion: Double      // 0–100
    let imageUrl: String?       // Optional image URL

    @State private var animatedCompletion: Double = 0

    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(AppTheme.surfaceSecondary, lineWidth: 6)

            // Progress circle with gradient
            Circle()
                .trim(from: 0, to: CGFloat(animatedCompletion / 100))
                .stroke(
                    LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: 6, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedCompletion)

            // Profile image
            ProfileImageView(imageUrl: imageUrl, size: 60)
                .scaleEffect(0.9)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).delay(0.2)) {
                animatedCompletion = completion
            }
        }
    }
}
