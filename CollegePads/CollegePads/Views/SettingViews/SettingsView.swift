import SwiftUI
import FirebaseAuth

struct SettingsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @ObservedObject var profileVM = ProfileViewModel.shared
    @State private var showVerification = false
    @State private var showBlockedUsers = false
    // Instead of using @AppStorage, we use local state for dark mode
    @State private var localDarkMode: Bool = false
    @State private var isUpdatingVisibility = false
    @State private var showVisibilityError = false
    @State private var visibilityErrorMessage = ""
    @State private var showVisibilitySuccess = false
    @State private var showReportProblem = false

    private let appVersion = "1.0.0"  // Update as needed.

    var body: some View {
        ZStack {
            AppTheme.backgroundGradient.ignoresSafeArea()

            Form {
                // Account Information Section.
                Section(header: Text("Account")
                            .font(AppTheme.subtitleFont)) {
                    HStack {
                        Text("Email")
                            .font(AppTheme.bodyFont)
                        Spacer()
                        Text(profileVM.userProfile?.email ?? "N/A")
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.gray)
                    }
                    HStack {
                        Text("Verified")
                            .font(AppTheme.bodyFont)
                        Spacer()
                        if profileVM.userProfile?.isEmailVerified == true {
                            Text("Yes")
                                .font(AppTheme.bodyFont)
                                .foregroundColor(.green)
                        } else {
                            Text("No")
                                .font(AppTheme.bodyFont)
                                .foregroundColor(.red)
                        }
                    }
                    if profileVM.userProfile?.isEmailVerified != true {
                        Button(action: { showVerification = true }) {
                            Text("Verify Now")
                                .font(AppTheme.bodyFont)
                                .foregroundColor(.blue)
                        }
                    }
                }

                // Privacy Section.
                Section(header: Text("Privacy")
                            .font(AppTheme.subtitleFont)) {

                    // Ultra-Modern Account Visibility Toggle
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Hide my profile from discovery")
                                    .font(.system(size: 16, weight: .semibold, design: .rounded))
                                    .foregroundColor(AppTheme.textPrimary)

                                Text("Control your visibility in discovery feeds")
                                    .font(.system(size: 13, weight: .medium))
                                    .foregroundColor(AppTheme.textSecondary)
                            }

                            Spacer()

                            // Modern toggle with enhanced styling
                            ZStack {
                                Toggle("", isOn: Binding(
                                    get: { profileVM.userProfile?.hideFromDiscovery ?? false },
                                    set: { newValue in
                                        updateDiscoveryVisibility(hidden: newValue)
                                    }
                                ))
                                .toggleStyle(ModernToggleStyle())
                                .disabled(isUpdatingVisibility)
                                .scaleEffect(1.1)

                                if isUpdatingVisibility {
                                    ProgressView()
                                        .scaleEffect(0.7)
                                        .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryColor))
                                }
                            }
                        }

                        Text("When enabled, your profile won't appear in other users' discovery feeds or search results")
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)
                            .padding(.leading, 4)

                        if showVisibilityError {
                            Text(visibilityErrorMessage)
                                .font(AppTheme.caption)
                                .foregroundColor(.red)
                                .padding(.leading, 4)
                                .padding(.top, 2)
                        }

                        if showVisibilitySuccess {
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 12))
                                Text("Setting updated successfully")
                                    .font(AppTheme.caption)
                                    .foregroundColor(.green)
                            }
                            .padding(.leading, 4)
                            .padding(.top, 2)
                        }
                    }
                    .padding(.vertical, 4)

                    NavigationLink(destination: BlockedUsersView()) {
                        Text("Manage Blocked Users")
                            .font(AppTheme.bodyFont)
                    }
                }

                // Appearance Section.
                Section(header: Text("Appearance")
                            .font(AppTheme.subtitleFont)) {
                    Toggle("Dark Mode", isOn: $localDarkMode)
                        .accessibilityLabel("Dark Mode Toggle")
                        .onChange(of: localDarkMode) { newValue in
                            // Update the stored value manually.
                            UserDefaults.standard.set(newValue, forKey: "isDarkMode")
                        }
                }

                // Support Section
                Section(header: Text("Support")
                            .font(AppTheme.subtitleFont)) {
                    Button(action: { showReportProblem = true }) {
                        HStack {
                            Image(systemName: "exclamationmark.bubble.fill")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundStyle(AppTheme.sexyGradient)

                            Text("Report a Problem")
                                .font(AppTheme.bodyFont)
                                .foregroundColor(AppTheme.textPrimary)

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                // Developer Tools Section (Debug builds only)
                #if DEBUG
                Section(header: Text("Developer Tools")
                            .font(AppTheme.subtitleFont)) {
                    NavigationLink(destination: CollegeSearchTestView()) {
                        HStack {
                            Image(systemName: "magnifyingglass.circle.fill")
                                .foregroundColor(.green)
                            Text("Test College Search")
                                .font(AppTheme.bodyFont)
                        }
                    }

                    NavigationLink(destination: DebugMatchInspectorView()) {
                        HStack {
                            Image(systemName: "bug.circle.fill")
                                .foregroundColor(.orange)
                            Text("Debug Match Inspector")
                                .font(AppTheme.bodyFont)
                        }
                    }
                }
                #endif

                // About Section.
                Section(header: Text("About")
                            .font(AppTheme.subtitleFont)) {
                    HStack {
                        Text("CollegePads")
                            .font(AppTheme.bodyFont)
                        Spacer()
                        Text("Version \(appVersion)")
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.gray)
                    }
                    NavigationLink(destination: AboutView()) {
                        Text("Learn More")
                            .font(AppTheme.bodyFont)
                    }
                }

                // Account Management Section.
                Section(header: Text("Account Management")
                            .font(AppTheme.subtitleFont)) {
                    NavigationLink(destination: DeleteAccountView().environmentObject(authViewModel)) {
                        Text("Delete Account")
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.red)
                    }
                }

                // Sign Out Section.
                Section {
                    Button(action: { authViewModel.signOut() }) {
                        Text("Log Out")
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.red)
                    }
                }
            }
            .scrollContentBackground(.hidden)
            .font(AppTheme.bodyFont)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("Settings")
                        .font(AppTheme.titleFont)
                        .foregroundColor(.primary)
                }
            }
            .sheet(isPresented: $showVerification) {
                VerificationView()
            }
            .sheet(isPresented: $showReportProblem) {
                ReportProblemView()
            }
        }
        .onAppear {
            // Set the local dark mode state from UserDefaults so the view doesn't dismiss on change.
            localDarkMode = UserDefaults.standard.bool(forKey: "isDarkMode")
        }
    }

    // MARK: - Helper Functions

    private func updateDiscoveryVisibility(hidden: Bool) {
        guard var updatedProfile = profileVM.userProfile else {
            print("❌ No user profile available to update")
            visibilityErrorMessage = "Profile not available. Please try again."
            showVisibilityError = true
            return
        }

        // Clear any previous errors/success and show loading state
        showVisibilityError = false
        showVisibilitySuccess = false
        isUpdatingVisibility = true

        updatedProfile.hideFromDiscovery = hidden

        profileVM.updateUserProfile(updatedProfile: updatedProfile) { [self] result in
            DispatchQueue.main.async {
                self.isUpdatingVisibility = false

                switch result {
                case .success:
                    print("✅ Discovery visibility updated: hidden = \(hidden)")
                    self.showVisibilityError = false
                    self.showVisibilitySuccess = true

                    // Show brief success feedback
                    let statusText = hidden ? "Profile hidden from discovery" : "Profile visible in discovery"
                    print("✅ \(statusText)")

                    // Auto-hide success message after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                        self.showVisibilitySuccess = false
                    }

                case .failure(let error):
                    print("❌ Failed to update discovery visibility: \(error.localizedDescription)")
                    self.visibilityErrorMessage = "Failed to update setting: \(error.localizedDescription)"
                    self.showVisibilityError = true

                    // Auto-hide error after 5 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        self.showVisibilityError = false
                    }
                }
            }
        }
    }
}

struct AboutView: View {
    var body: some View {
        ZStack {
            AppTheme.backgroundGradient.ignoresSafeArea()
            VStack(spacing: 20) {
                Text("CollegePads")
                    .font(AppTheme.titleFont)
                    .bold()
                Text("A production‑ready app to help college students find the perfect roommate and housing. Built with scalability and user experience in mind.")
                    .font(AppTheme.bodyFont)
                    .multilineTextAlignment(.center)
                    .padding()
                Spacer()
            }
            .padding()
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("About")
                        .font(AppTheme.titleFont)
                        .foregroundColor(.primary)
                }
            }
        }
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SettingsView().environmentObject(AuthViewModel())
        }
    }
}
