import SwiftUI

struct TabBarView: View {
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Home Tab - moved to first position for better UX
            HomeView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                            .font(.system(size: 20, weight: selectedTab == 0 ? .bold : .medium))
                            .scaleEffect(selectedTab == 0 ? 1.1 : 1.0)
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                        Text("Home")
                            .font(.caption)
                            .fontWeight(selectedTab == 0 ? .semibold : .regular)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                    }
                }
                .tag(0)

            // Swipe Tab with enhanced flame icon
            SwipeDeckView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 1 ? "flame.fill" : "flame")
                            .font(.system(size: 20, weight: selectedTab == 1 ? .bold : .medium))
                            .scaleEffect(selectedTab == 1 ? 1.1 : 1.0)
                            .foregroundStyle(selectedTab == 1 ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.primary))
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                        Text("Discover")
                            .font(.caption)
                            .fontWeight(selectedTab == 1 ? .semibold : .regular)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                    }
                }
                .tag(1)

            // Messages Tab with enhanced bubble icon
            CombinedMatchesChatView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 2 ? "bubble.left.and.bubble.right.fill" : "bubble.left.and.bubble.right")
                            .font(.system(size: 20, weight: selectedTab == 2 ? .bold : .medium))
                            .scaleEffect(selectedTab == 2 ? 1.1 : 1.0)
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                        Text("Messages")
                            .font(.caption)
                            .fontWeight(selectedTab == 2 ? .semibold : .regular)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                    }
                }
                .tag(2)

            // Global Search Tab with premium gold styling
            GlobalSearchView()
                .tabItem {
                    VStack {
                        ZStack {
                            // ENHANCED: Force premium gold styling with explicit colors
                            Image(systemName: selectedTab == 3 ? "magnifyingglass.circle.fill" : "magnifyingglass")
                                .font(.system(size: 24, weight: selectedTab == 3 ? .bold : .medium))
                                .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                .foregroundColor(Color.yellow) // Force explicit yellow color
                                .overlay(
                                    // Additional gradient overlay to ensure visibility
                                    Image(systemName: selectedTab == 3 ? "magnifyingglass.circle.fill" : "magnifyingglass")
                                        .font(.system(size: 24, weight: selectedTab == 3 ? .bold : .medium))
                                        .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                        .foregroundStyle(
                                            LinearGradient(
                                                colors: [
                                                    Color.yellow,
                                                    Color.orange,
                                                    Color.yellow
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .blendMode(.multiply)
                                )
                                .shadow(color: Color.yellow, radius: 10, x: 0, y: 5)
                                .shadow(color: Color.orange, radius: 15, x: 0, y: 8)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)

                            // ENHANCED: More prominent crown indicator
                            Image(systemName: "crown.fill")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(.yellow)
                                .offset(x: 15, y: -15)
                                .shadow(color: Color.yellow, radius: 4, x: 0, y: 2)
                                .scaleEffect(selectedTab == 3 ? 1.2 : 1.0)

                            // ENHANCED: Always visible sparkles with stronger animation
                            Image(systemName: "sparkles")
                                .font(.system(size: selectedTab == 3 ? 12 : 10, weight: .bold))
                                .foregroundColor(.white)
                                .offset(x: 16, y: -12)
                                .opacity(selectedTab == 3 ? 1.0 : 0.8)
                                .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                .shadow(color: Color.yellow, radius: 3, x: 0, y: 1)
                                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: selectedTab)
                        }

                        Text("Search")
                            .font(.caption)
                            .fontWeight(selectedTab == 3 ? .bold : .regular)
                            .foregroundColor(Color.yellow) // Force explicit yellow color
                            .shadow(color: Color.yellow, radius: 3, x: 0, y: 1)
                            .shadow(color: Color.orange, radius: 5, x: 0, y: 2)
                            .scaleEffect(selectedTab == 3 ? 1.1 : 1.0)
                    }
                }
                .tag(3)
        }
        .accentColor(AppTheme.primaryColor)
        .onAppear {
            // CRITICAL FIX: Completely disable UITabBar appearance to allow custom styling
            let appearance = UITabBarAppearance()
            appearance.configureWithTransparentBackground()

            // Completely transparent background for modern look
            appearance.backgroundColor = UIColor.clear
            appearance.backgroundEffect = nil
            appearance.shadowColor = UIColor.clear
            appearance.shadowImage = UIImage()

            // Remove default indicator for custom styling
            appearance.selectionIndicatorTintColor = UIColor.clear

            // CRITICAL: Remove ALL icon and text color overrides to allow custom styling
            appearance.stackedLayoutAppearance.normal.iconColor = nil
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [:]
            appearance.stackedLayoutAppearance.selected.iconColor = nil
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [:]

            // Apply minimal appearance
            UITabBar.appearance().standardAppearance = appearance
            UITabBar.appearance().scrollEdgeAppearance = appearance

            // Remove any background styling
            UITabBar.appearance().backgroundColor = UIColor.clear
            UITabBar.appearance().isTranslucent = true
            UITabBar.appearance().barTintColor = UIColor.clear
            UITabBar.appearance().tintColor = UIColor.clear
            UITabBar.appearance().unselectedItemTintColor = UIColor.clear
        }
    }
}

struct TabBarView_Previews: PreviewProvider {
    static var previews: some View {
        TabBarView()
    }
}
