import SwiftUI
import FirebaseAuth
import FirebaseFirestore

struct RootView: View {
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var profileViewModel = ProfileViewModel.shared
    @State private var showOnboarding: Bool = false
    @State private var showSplash: Bool = true
    @State private var showLocationPermission: Bool = false
    @AppStorage("isDarkMode") private var isDarkMode: Bool = true
    @AppStorage("locationPermissionRequested") private var locationPermissionRequested: Bool = false

    var body: some View {
        ZStack {
            if showSplash {
                // Beautiful splash screen
                SplashView {
                    withAnimation(.easeOut(duration: 0.5)) {
                        showSplash = false
                        // Check if we need to show location permission
                        if !locationPermissionRequested {
                            showLocationPermission = true
                        }
                    }
                }
                .transition(.opacity)
            } else if showLocationPermission {
                // Location permission request
                LocationPermissionView(isLocationPermissionGranted: Binding(
                    get: { false },
                    set: { granted in
                        withAnimation(.easeOut(duration: 0.5)) {
                            showLocationPermission = false
                            locationPermissionRequested = true
                        }
                    }
                ))
                .transition(.opacity)
            } else {
                // Main app content
                NavigationView {
                    Group {
                        if authViewModel.userSession == nil {
                            AuthenticationView()
                                .environmentObject(authViewModel)
                        } else {
                            TabBarView()
                                .environmentObject(authViewModel)
                                .environmentObject(profileViewModel)
                        }
                    }
                    .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
                }
                .transition(.opacity)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .preferredColorScheme(isDarkMode ? .dark : .light)
        .onAppear {
            // Start auth listening immediately but don't show UI until splash completes
            authViewModel.listenToAuthState()
        }
        .onChange(of: authViewModel.userSession) { _, userSession in
            // Start analytics session when user is authenticated
            if let userSession = userSession {
                AnalyticsManager.shared.startSession(userID: userSession.uid)
            }
        }
        .onChange(of: showSplash) { _, splashVisible in
            if !splashVisible {
                // Splash completed, now check onboarding
                checkOnboardingStatus()
            }
        }
        .onChange(of: authViewModel.userSession) { _, newSession in
            if newSession != nil {
                // User just signed in/up - check if they need onboarding
                print("🔄 RootView: User session changed, checking onboarding status")
                print("🔍 RootView: New session UID: \(newSession?.uid ?? "nil")")

                // Add a small delay to ensure Firebase state is fully updated
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.checkOnboardingStatus()
                }
            } else {
                // User signed out - hide onboarding
                print("🔄 RootView: User signed out, hiding onboarding")
                showOnboarding = false
            }
        }
        .fullScreenCover(isPresented: $showOnboarding) {
            if authViewModel.userSession == nil {
                TutorialOnboardingView()
                    .onAppear {
                        print("🎭 RootView: Showing TutorialOnboardingView (no user session)")
                    }
            } else {
                NewProfileSetupOnboardingView()
                    .onAppear {
                        print("🎭 RootView: Showing NewProfileSetupOnboardingView (user session exists)")
                    }
            }
        }
    }

    /// Enhanced onboarding check with better debugging and timing
    private func checkOnboardingStatus() {
        let completed = UserDefaults.standard.bool(forKey: "onboardingCompleted")
        let hasSession = authViewModel.userSession != nil
        let userUID = authViewModel.userSession?.uid ?? "nil"

        print("🔍 RootView: Checking onboarding status:")
        print("   - Has user session: \(hasSession)")
        print("   - User UID: \(userUID)")
        print("   - Onboarding completed: \(completed)")
        print("   - Current showOnboarding: \(showOnboarding)")

        // Enhanced logic for onboarding trigger
        if hasSession && !completed {
            print("🔄 RootView: User authenticated but onboarding not completed - showing onboarding")

            // Force show onboarding for authenticated users without completion
            DispatchQueue.main.async {
                self.showOnboarding = true
            }
        } else if !hasSession && !completed {
            print("🔄 RootView: No user session and onboarding not completed - showing tutorial")

            // Show tutorial for non-authenticated users
            DispatchQueue.main.async {
                self.showOnboarding = true
            }
        } else if hasSession && completed {
            print("🔄 RootView: User authenticated and onboarding completed - hiding onboarding")

            // Hide onboarding for completed users
            DispatchQueue.main.async {
                self.showOnboarding = false
            }
        } else {
            print("🔄 RootView: No session and completed (edge case) - hiding onboarding")

            // Edge case: no session but marked completed
            DispatchQueue.main.async {
                self.showOnboarding = false
            }
        }

        print("   - Final showOnboarding: \(showOnboarding)")

        // Additional debugging: Check if user was just created
        if let user = authViewModel.userSession {
            let creationTime = user.metadata.creationDate
            let now = Date()
            let timeSinceCreation = now.timeIntervalSince(creationTime ?? now)

            print("🔍 RootView: User creation time: \(creationTime?.description ?? "unknown")")
            print("🔍 RootView: Time since creation: \(timeSinceCreation) seconds")

            // If user was created very recently (within 30 seconds), definitely show onboarding
            if timeSinceCreation < 30 && !completed {
                print("🚀 RootView: Recently created user detected, forcing onboarding")
                DispatchQueue.main.async {
                    self.showOnboarding = true
                }
            }
        }
    }


    struct RootView_Previews: PreviewProvider {
        static var previews: some View {
            RootView()
        }
    }
}
