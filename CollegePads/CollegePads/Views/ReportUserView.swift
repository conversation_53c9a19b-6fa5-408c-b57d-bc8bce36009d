import SwiftUI

struct ReportUserView: View {
    @StateObject private var viewModel = SafetyViewModel()
    let reportedUserID: String  // UID of the user being reported

    @State private var selectedCategory: ReportCategory = .inappropriate
    @State private var reason: String = ""
    @State private var isSubmitting: Bool = false
    @State private var showSuccessMessage: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Sexy gradient background
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Header with icon
                        headerSection

                        // Report categories
                        categorySection

                        // Additional details
                        detailsSection

                        // Submit button
                        submitSection

                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        if #available(iOS 15.0, *) {
                            dismiss()
                        } else {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                    .foregroundStyle(AppTheme.sexyGradient)
                    .font(.custom("AvenirNext-Medium", size: 16))
                }
            }
            .alert("Report Submitted", isPresented: $showSuccessMessage) {
                Button("OK") {
                    if #available(iOS 15.0, *) {
                        dismiss()
                    } else {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            } message: {
                Text("Thank you for helping keep our community safe. We'll review this report promptly.")
            }
            .alert(item: Binding(
                get: {
                    if let errorMessage = viewModel.errorMessage {
                        return GenericAlertError(message: errorMessage)
                    }
                    return nil
                },
                set: { _ in viewModel.errorMessage = nil }
            )) { alertError in
                Alert(title: Text("Error"), message: Text(alertError.message), dismissButton: .default(Text("OK")))
            }
        }
    }

    // MARK: - View Components

    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)

                Image(systemName: "shield.fill")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("Report User")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)

                Text("Help us maintain a safe and respectful community")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }

    private var categorySection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("What's the issue?")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: AppTheme.spacing12) {
                ForEach(ReportCategory.allCases, id: \.self) { category in
                    ReportCategoryCard(
                        category: category,
                        isSelected: selectedCategory == category,
                        onSelect: { selectedCategory = category }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var detailsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("Additional Details (Optional)")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)

            Text("Please provide any additional context that might help us understand the situation better.")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)

            ZStack(alignment: .topLeading) {
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(.ultraThinMaterial)
                    .frame(height: 120)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )

                TextEditor(text: $reason)
                    .background(Color.clear)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textPrimary)
                    .padding(AppTheme.spacing12)

                if reason.isEmpty {
                    Text("Describe what happened...")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textTertiary)
                        .padding(.horizontal, AppTheme.spacing16)
                        .padding(.vertical, AppTheme.spacing20)
                        .allowsHitTesting(false)
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private var submitSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            Button(action: submitReport) {
                HStack(spacing: AppTheme.spacing12) {
                    if isSubmitting {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "shield.checkered")
                            .font(.system(size: 18, weight: .bold))
                    }

                    Text(isSubmitting ? "Submitting Report..." : "Submit Report")
                        .font(.custom("AvenirNext-Bold", size: 18))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .fill(canSubmit ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.gray))
                )
                .shadow(color: canSubmit ? AppTheme.primaryColor.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
            }
            .disabled(!canSubmit || isSubmitting)
            .scaleEffect(isSubmitting ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSubmitting)

            Text("Reports are reviewed within 24 hours. False reports may result in account restrictions.")
                .font(.custom("AvenirNext-Medium", size: 12))
                .foregroundColor(AppTheme.textTertiary)
                .multilineTextAlignment(.center)
        }
    }

    private var canSubmit: Bool {
        true // Always allow submission since category is required and reason is optional
    }

    private func submitReport() {
        isSubmitting = true
        HapticFeedbackManager.shared.generateImpact(style: .medium)

        let fullReason = "\(selectedCategory.rawValue): \(reason.isEmpty ? selectedCategory.description : reason)"

        viewModel.reportUser(reportedUserID: reportedUserID, reason: fullReason) { result in
            DispatchQueue.main.async {
                isSubmitting = false
                switch result {
                case .success:
                    HapticFeedbackManager.shared.generateNotification(.success)
                    showSuccessMessage = true
                case .failure(let error):
                    HapticFeedbackManager.shared.generateNotification(.error)
                    print("Report submission failed: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - Supporting Types

enum ReportCategory: String, CaseIterable {
    case inappropriate = "Inappropriate Content"
    case harassment = "Harassment"
    case spam = "Spam"
    case fake = "Fake Profile"
    case safety = "Safety Concerns"
    case other = "Other"

    var icon: String {
        switch self {
        case .inappropriate: return "exclamationmark.triangle.fill"
        case .harassment: return "person.2.slash.fill"
        case .spam: return "envelope.badge.fill"
        case .fake: return "person.crop.circle.badge.xmark"
        case .safety: return "shield.slash.fill"
        case .other: return "ellipsis.circle.fill"
        }
    }

    var description: String {
        switch self {
        case .inappropriate: return "Inappropriate photos, messages, or behavior"
        case .harassment: return "Bullying, threats, or unwanted contact"
        case .spam: return "Promotional content or repeated messages"
        case .fake: return "Fake photos, information, or impersonation"
        case .safety: return "Concerns about user safety or wellbeing"
        case .other: return "Other issues not listed above"
        }
    }
}

struct ReportCategoryCard: View {
    let category: ReportCategory
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        let circleFill = isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial)
        let circleStroke = isSelected ? Color.clear : Color.white.opacity(0.3)
        let iconColor = isSelected ? Color.white : AppTheme.textSecondary
        let textColor = isSelected ? AppTheme.textPrimary : AppTheme.textSecondary
        let backgroundFill = isSelected ? AnyShapeStyle(.ultraThinMaterial) : AnyShapeStyle(Color.clear)
        let borderStroke = isSelected ? AppTheme.primaryColor.opacity(0.5) : Color.white.opacity(0.2)

        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            onSelect()
        }) {
            VStack(spacing: AppTheme.spacing8) {
                ZStack {
                    Circle()
                        .fill(circleFill)
                        .frame(width: 40, height: 40)
                        .overlay(
                            Circle()
                                .stroke(circleStroke, lineWidth: 1)
                        )

                    Image(systemName: category.icon)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(iconColor)
                }

                Text(category.rawValue)
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(textColor)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(AppTheme.spacing12)
            .frame(maxWidth: .infinity, minHeight: 80)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(backgroundFill)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .stroke(borderStroke, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct ReportUserView_Previews: PreviewProvider {
    static var previews: some View {
        ReportUserView(reportedUserID: "dummyUserID")
    }
}
