import SwiftUI
import FirebaseFirestore

struct CombinedMatchesChatView: View {
    @StateObject private var matchesVM = MatchesDashboardViewModel()
    @StateObject private var chatsVM = ChatsListViewModel()
    @State private var showAllMatches: Bool = false
    @State private var navigateToChat: String? = nil
    @State private var searchText: String = ""
    @State private var isSearching: Bool = false

    // Use shared ChatManager instance
    private let chatManager = ChatManager.shared

    var body: some View {
        NavigationView {
            ZStack {
                // Stunning animated background with enhanced gradients
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea()

                // Subtle animated overlay for depth
                AnimatedBackground()
                    .opacity(0.3)

                VStack(spacing: 0) {
                    // ENHANCED TINDER-STYLE MATCHES BAR
                    if !isSearching {
                        enhancedMatchesBar
                    }

                    // Inline Search Bar
                    if isSearching {
                        inlineSearchBar
                    }

                    // Beautiful divider with animated gradient
                    if !isSearching {
                        HStack {
                            Rectangle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.clear,
                                            AppTheme.primaryColor.opacity(0.4),
                                            AppTheme.accentColor.opacity(0.3),
                                            Color.clear
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(height: 1.5)
                                .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 2, x: 0, y: 1)
                        }
                        .padding(.horizontal, AppTheme.spacing20)
                        .padding(.vertical, AppTheme.spacing8)
                    }

                    // Enhanced Chat List with beautiful styling
                    enhancedChatsListView
                }
                .toolbar {
                    ToolbarItem(placement: .principal) {
                        VStack(spacing: 2) {
                            Text("Messages")
                                .font(.custom("AvenirNext-Bold", size: 20))
                                .fontWeight(.bold)
                                .foregroundStyle(AppTheme.sexyGradient)
                                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)

                            Text("Stay Connected")
                                .font(.custom("AvenirNext-Medium", size: 12))
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }

                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isSearching.toggle()
                                if !isSearching {
                                    searchText = ""
                                }
                            }
                        }) {
                            ZStack {
                                // Regular background for search icon
                                Circle()
                                    .fill(AppTheme.glassEffect)
                                    .frame(width: 40, height: 40)

                                Image(systemName: isSearching ? "xmark" : "magnifyingglass")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(AppTheme.textPrimary)
                                    .frame(width: 32, height: 32)
                                    .background(AppTheme.glassEffect)
                                    .clipShape(Circle())
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1.5)
                                    )
                                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            }
                        }
                    }
                }
                .onAppear {
                    print("💬 CombinedMatchesChatView: View appeared - triggering optimized refresh")

                    // Only refresh if data is empty or stale (avoid excessive refreshing)
                    if chatsVM.chats.isEmpty || matchesVM.matches.isEmpty {
                        matchesVM.forceRefreshOnTabOpen()
                        chatsVM.forceRefreshOnTabOpen()
                    }

                    // Start auto-refresh with longer intervals to reduce load
                    matchesVM.startAutoRefresh()
                    chatsVM.startAutoRefresh()

                    // Preload chat participant profiles after a short delay to avoid conflicts
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        preloadChatProfiles()
                    }
                }
                .onDisappear {
                    print("💬 CombinedMatchesChatView: View disappeared - stopping auto-refresh")
                    matchesVM.stopAutoRefresh() // Stop refresh when view disappears
                    chatsVM.stopAutoRefresh()
                }
                .refreshable {
                    // Pull-to-refresh functionality (manual)
                    print("💬 CombinedMatchesChatView: Manual refresh triggered")
                    matchesVM.manualRefresh()
                    chatsVM.fetchChats()
                }
            }
        }
        .sheet(isPresented: $showAllMatches) {
            AllMatchesView()
        }
        .background(
            NavigationLink(
                destination: Group {
                    if let chatID = navigateToChat {
                        ChatConversationView(
                            viewModel: ChatConversationViewModel(chatID: chatID),
                            chatPartnerID: ""
                        )
                    } else {
                        EmptyView()
                    }
                },
                isActive: Binding(
                    get: { navigateToChat != nil },
                    set: { if !$0 { navigateToChat = nil } }
                )
            ) {
                EmptyView()
            }
            .hidden()
        )
    }

    // MARK: - Sleek Compact Search Bar
    private var inlineSearchBar: some View {
        VStack(spacing: AppTheme.spacing8) {
            HStack(spacing: AppTheme.spacing12) {
                // Compact search field - same height as search icon
                HStack(spacing: AppTheme.spacing8) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppTheme.textSecondary)

                    TextField("Search...", text: $searchText)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppTheme.textPrimary)
                        .textFieldStyle(PlainTextFieldStyle())

                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 14))
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }
                }
                .padding(.horizontal, AppTheme.spacing12)
                .padding(.vertical, AppTheme.spacing8)
                .frame(height: 32) // Compact height - same as search icon
                .background(AppTheme.glassEffect)
                .cornerRadius(AppTheme.radiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )

                // Cancel button
                Button("Cancel") {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        isSearching = false
                        searchText = ""
                    }
                }
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.primaryColor)
            }

            // Search results count
            if !searchText.isEmpty {
                HStack {
                    Text("\(filteredChats.count) result\(filteredChats.count == 1 ? "" : "s")")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(AppTheme.textSecondary)
                    Spacer()
                }
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.vertical, AppTheme.spacing8)
        .background(
            ZStack {
                AppTheme.glassEffect
                Color.white.opacity(0.05)
            }
        )
    }

    // MARK: - Enhanced Filtered Chats with Name Search
    private var filteredChats: [ChatListItem] {
        if searchText.isEmpty {
            return chatsVM.chats.sorted { $0.lastMessageAt > $1.lastMessageAt }
        } else {
            // Enhanced filter with profile name search
            let searchLower = searchText.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

            let filtered = chatsVM.chats.filter { chat in
                // Get the other participant's ID
                let otherUserID = chat.participants.first { $0 != chatsVM.currentUserID }

                // Search in message content
                let messageMatch = chat.lastMessage.localizedCaseInsensitiveContains(searchText)

                // Search in participant ID (fallback)
                let userIDMatch = otherUserID?.localizedCaseInsensitiveContains(searchText) ?? false

                // Search in profile names (enhanced) - only use cached profiles
                var nameMatch = false
                if let otherUserID = otherUserID,
                   let profile = ProfileLoaderService.shared.getCachedProfile(userID: otherUserID) {

                    let firstName = profile.firstName?.lowercased() ?? ""
                    let lastName = profile.lastName?.lowercased() ?? ""
                    let fullName = "\(firstName) \(lastName)".trimmingCharacters(in: .whitespacesAndNewlines)
                    let reverseName = "\(lastName) \(firstName)".trimmingCharacters(in: .whitespacesAndNewlines)

                    // Improved name matching - exact word matches and starts with
                    nameMatch = firstName.hasPrefix(searchLower) ||
                               lastName.hasPrefix(searchLower) ||
                               fullName.hasPrefix(searchLower) ||
                               reverseName.hasPrefix(searchLower) ||
                               firstName.contains(" \(searchLower)") ||
                               lastName.contains(" \(searchLower)") ||
                               fullName.contains(" \(searchLower)")
                }

                return messageMatch || userIDMatch || nameMatch
            }

            // Sort by relevance: exact matches first, then by last message time
            return filtered.sorted { chat1, chat2 in
                let chat1Score = relevanceScore(for: chat1)
                let chat2Score = relevanceScore(for: chat2)

                if chat1Score != chat2Score {
                    return chat1Score > chat2Score
                }
                return chat1.lastMessageAt > chat2.lastMessageAt
            }
        }
    }

    private func relevanceScore(for chat: ChatListItem) -> Int {
        let searchLower = searchText.lowercased()
        var score = 0

        // Check last message for exact match
        if chat.lastMessage.lowercased().contains(searchLower) {
            score += 10
        }

        // Check participant ID (could be enhanced with profile names)
        let otherUserID = chat.participants.first { $0 != chatsVM.currentUserID }
        if let otherUserID = otherUserID, otherUserID.lowercased().contains(searchLower) {
            score += 5
        }

        return score
    }

    // MARK: - Enhanced Matches Bar (Beautiful Tinder-style with Glass Morphism)
    private var enhancedMatchesBar: some View {
        VStack(spacing: AppTheme.spacing8) {
            // Compact header with gradient text
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("New Matches")
                        .font(AppTheme.headline)
                        .fontWeight(.bold)
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text("\(newMatches.count) waiting")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                }

                Spacer()

                Button(action: {
                    showAllMatches = true
                }) {
                    HStack(spacing: 4) {
                        Text("See All")
                            .font(AppTheme.caption)
                            .fontWeight(.semibold)

                        Image(systemName: "arrow.right.circle.fill")
                            .font(.system(size: 12))
                    }
                    .foregroundStyle(AppTheme.primaryGradient)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(AppTheme.glassEffect)
                    .cornerRadius(AppTheme.radiusMedium)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                }
            }
            .padding(.horizontal, AppTheme.spacing20)
            .padding(.top, AppTheme.spacing12)

            // Compact horizontal scroll view with glass morphism
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: AppTheme.spacing16) {
                    // Enhanced "Find More" button with subtle glow
                    Button(action: {
                        // Navigate to swipe view
                    }) {
                        VStack(spacing: AppTheme.spacing6) {
                            ZStack {
                                // Base circle with gradient
                                Circle()
                                    .fill(AppTheme.sexyGradient)
                                    .frame(width: 64, height: 64)

                                // Subtle outer glow ring
                                Circle()
                                    .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 2)
                                    .frame(width: 68, height: 68)
                                    .blur(radius: 2)

                                // Inner highlight
                                Circle()
                                    .fill(
                                        RadialGradient(
                                            gradient: Gradient(colors: [
                                                Color.white.opacity(0.3),
                                                Color.clear
                                            ]),
                                            center: .topLeading,
                                            startRadius: 5,
                                            endRadius: 30
                                        )
                                    )
                                    .frame(width: 64, height: 64)

                                Image(systemName: "heart.circle.fill")
                                    .font(.system(size: 28, weight: .medium))
                                    .foregroundColor(.white)
                                    .shadow(color: .black.opacity(0.2), radius: 1, x: 0, y: 1)
                            }

                            Text("Find")
                                .font(.system(size: 11, weight: .bold))
                                .foregroundStyle(AppTheme.sexyGradient)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    // Compact match profiles with enhanced tap-to-chat
                    ForEach(newMatches.prefix(10)) { match in
                        let candidateID = candidateID(for: match)
                        CompactMatchCard(
                            candidateID: candidateID,
                            isNewMatch: true,
                            onTap: {
                                handleMatchTap(match: match, candidateID: candidateID)
                            }
                        )
                    }
                }
                .padding(.horizontal, AppTheme.spacing20)
            }
            .frame(height: 90) // Compact height similar to Tinder
        }
        .padding(.bottom, AppTheme.spacing12)
        .background(
            ZStack {
                AppTheme.glassEffect
                Color.white.opacity(0.05)
            }
        )
        .overlay(
            Rectangle()
                .stroke(Color.white.opacity(0.1), lineWidth: 1)
                .blur(radius: 0.5)
        )
    }

    // MARK: - Enhanced Chat List View
    private var enhancedChatsListView: some View {
        ZStack {
            if let errorMessage = chatsVM.errorMessage {
                // Error state view
                errorStateView(error: errorMessage)

            } else if filteredChats.isEmpty && !chatsVM.isLoading {
                // Enhanced empty state - show different messages for search vs no chats
                if isSearching && !searchText.isEmpty {
                    searchEmptyStateView
                } else if chatsVM.chats.isEmpty {
                    enhancedEmptyChatsView
                } else {
                    searchEmptyStateView
                }

            } else if chatsVM.isLoading && chatsVM.chats.isEmpty {
                // Only show loading when initially loading and no chats exist yet
                VStack(spacing: AppTheme.spacing16) {
                    ZStack {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 50, height: 50)
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: UUID())

                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    }

                    Text("Loading conversations...")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)

            } else {
                // Beautiful chat list with filtered results
                ScrollView {
                    LazyVStack(spacing: AppTheme.spacing12) {
                        ForEach(filteredChats) { chat in
                            EnhancedChatListCard(
                                chat: chat,
                                currentUserID: chatsVM.currentUserID ?? "",
                                profileLoader: ProfileLoaderService.shared
                            )
                        }
                    }
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.top, AppTheme.spacing8)
                }
                .refreshable {
                    chatsVM.fetchChats()
                }
            }
        }
    }

    // MARK: - Error State View
    private func errorStateView(error: String) -> some View {
        VStack(spacing: AppTheme.spacing24) {
            ZStack {
                Circle()
                    .fill(Color.red.opacity(0.2))
                    .frame(width: 80, height: 80)

                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(.red)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("Loading Error")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(AppTheme.textPrimary)

                Text(error)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: {
                chatsVM.clearLoadingState()
                chatsVM.forceRefreshOnTabOpen()
            }) {
                HStack(spacing: AppTheme.spacing8) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16, weight: .medium))
                    Text("Try Again")
                        .font(.custom("AvenirNext-Bold", size: 16))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing20)
                .padding(.vertical, AppTheme.spacing12)
                .background(AppTheme.primaryColor)
                .cornerRadius(AppTheme.radiusMedium)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(AppTheme.spacing24)
    }

    // MARK: - Search Empty State View
    private var searchEmptyStateView: some View {
        VStack(spacing: AppTheme.spacing24) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)

                Image(systemName: "magnifyingglass")
                    .font(.system(size: 32, weight: .light))
                    .foregroundColor(.white)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("No Results Found")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Try searching with different keywords")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(AppTheme.spacing24)
    }

    // MARK: - Enhanced Empty Chats View
    private var enhancedEmptyChatsView: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Animated message icon with glow
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 120, height: 120)
                    .neonGlow(color: AppTheme.primaryColor)

                Image(systemName: "message.circle.fill")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            .scaleEffect(1.0)
            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: UUID())

            VStack(spacing: AppTheme.spacing16) {
                Text("Start Your First Conversation")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .multilineTextAlignment(.center)

                Text("Connect with your matches and discover your perfect roommate through meaningful conversations!")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.spacing24)
            }

            Button(action: {
                // Navigate to discovery - handled by parent
            }) {
                HStack(spacing: AppTheme.spacing12) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 20, weight: .semibold))
                    Text("Find Your Match")
                        .font(.custom("AvenirNext-Bold", size: 18))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing32)
                .padding(.vertical, AppTheme.spacing16)
                .background(AppTheme.sexyGradient)
                .cornerRadius(AppTheme.radiusLarge)
                .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 12, x: 0, y: 6)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
            }
            .scaleEffect(1.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: UUID())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(AppTheme.spacing24)
    }

    // MARK: - Helper Methods

    /// Filter matches to show only new ones (without conversation started)
    private var newMatches: [MatchItem] {
        // Use the enhanced filtering from MatchesDashboardViewModel
        matchesVM.newMatches
    }

    /// Optimized match-to-chat flow
    private func handleMatchTap(match: MatchItem, candidateID: String) {
        guard let currentUserID = matchesVM.currentUserID else { return }

        // Create or get existing chat
        chatManager.createOrGetChat(between: currentUserID, and: candidateID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let chatID):
                    // Update match status and navigate
                    ChatManager.shared.markConversationStarted(for: [currentUserID, candidateID], chatID: chatID)
                    navigateToChat = chatID

                    // Refresh UI data
                    matchesVM.refreshMatches()
                    chatsVM.fetchChats()

                case .failure(let error):
                    print("❌ Failed to create chat: \(error.localizedDescription)")
                }
            }
        }
    }

    /// Helper to extract the candidateID from a MatchItem.
    private func candidateID(for match: MatchItem) -> String {
        return match.participants.first(where: { $0 != (matchesVM.currentUserID ?? "") }) ?? "unknown"
    }

    /// Check if this is a new match (within last 24 hours)
    private func isNewMatch(_ match: MatchItem) -> Bool {
        let dayAgo = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        return match.createdAt > dayAgo
    }

    /// Preload chat participant profiles to avoid publishing warnings and improve search
    private func preloadChatProfiles() {
        print("🔄 CombinedMatchesChatView: Preloading chat profiles for \(chatsVM.chats.count) chats")

        // Preload profiles for all chat participants in background with rate limiting
        Task {
            let uniqueUserIDs = Set(chatsVM.chats.compactMap { chat in
                chat.participants.first(where: { $0 != chatsVM.currentUserID })
            })

            print("🔄 CombinedMatchesChatView: Found \(uniqueUserIDs.count) unique users to preload")

            for (index, otherUserID) in uniqueUserIDs.enumerated() {
                // Only load if not already cached to avoid spam
                if ProfileLoaderService.shared.getCachedProfile(userID: otherUserID) == nil {
                    print("🔄 CombinedMatchesChatView: Loading profile for user \(index + 1)/\(uniqueUserIDs.count): \(otherUserID)")

                    // Use the safe version that doesn't update @Published properties
                    ProfileLoaderService.shared.loadProfile(candidateID: otherUserID)

                    // Add small delay between requests to avoid overwhelming Firebase
                    if index < uniqueUserIDs.count - 1 {
                        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
                    }
                } else {
                    print("✅ CombinedMatchesChatView: Profile already cached for user: \(otherUserID)")
                }
            }

            print("✅ CombinedMatchesChatView: Completed preloading chat profiles")
        }
    }
}

// MARK: - Enhanced Compact Match Card Component (Tinder-style)
struct CompactMatchCard: View {
    let candidateID: String
    let isNewMatch: Bool
    let onTap: () -> Void

    @ObservedObject private var profileLoader = ProfileLoaderService.shared
    @State private var isPressed = false
    @State private var showCelebration = false
    @State private var shimmerOffset: CGFloat = -200

    var body: some View {
        Button(action: {
            // Enhanced haptic feedback
            UIImpactFeedbackGenerator(style: .light).impactOccurred()

            // Smooth celebration animation
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                showCelebration = true
            }

            // Execute action immediately for responsiveness
            onTap()
        }) {
            VStack(spacing: AppTheme.spacing4) {
                ZStack {
                    // Enhanced profile image with modern styling
                    if let imageUrl = profileLoader.user?.profileImageUrls?.first,
                       let url = URL(string: imageUrl) {
                        AsyncImage(url: url) { image in
                            image
                                .resizable()
                                .scaledToFill()
                                .frame(width: 60, height: 60)
                                .clipped()
                        } placeholder: {
                            ZStack {
                                Circle()
                                    .fill(AppTheme.sexyGradient)
                                    .overlay(
                                        // Animated shimmer effect
                                        Circle()
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [
                                                        Color.clear,
                                                        Color.white.opacity(0.3),
                                                        Color.clear
                                                    ]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                            .offset(x: shimmerOffset)
                                            .animation(
                                                .linear(duration: 1.5)
                                                .repeatForever(autoreverses: false),
                                                value: shimmerOffset
                                            )
                                    )

                                ProgressView()
                                    .tint(.white)
                                    .scaleEffect(0.7)
                            }
                        }
                        .frame(width: 60, height: 60)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.8),
                                            AppTheme.primaryColor.opacity(0.4),
                                            Color.white.opacity(0.2)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )
                        .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    } else {
                        ZStack {
                            Circle()
                                .fill(AppTheme.sexyGradient)
                                .frame(width: 60, height: 60)

                            Image(systemName: "person.fill")
                                .font(.system(size: 22, weight: .medium))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.2), radius: 1, x: 0, y: 1)
                        }
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.8),
                                            AppTheme.primaryColor.opacity(0.4),
                                            Color.white.opacity(0.2)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1.5
                                )
                        )
                        .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        .overlay(
                            // Shimmer for placeholder
                            Circle()
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.clear,
                                            Color.white.opacity(0.3),
                                            Color.clear
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .offset(x: shimmerOffset)
                                .animation(
                                    .linear(duration: 1.5)
                                    .repeatForever(autoreverses: false),
                                    value: shimmerOffset
                                )
                        )
                        .clipped()
                    }

                    // Elegant new match indicator
                    if isNewMatch {
                        VStack {
                            HStack {
                                Spacer()
                                ZStack {
                                    Circle()
                                        .fill(AppTheme.sexyGradient)
                                        .frame(width: 16, height: 16)

                                    Image(systemName: "sparkles")
                                        .font(.system(size: 7, weight: .bold))
                                        .foregroundColor(.white)
                                }
                                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)
                                .scaleEffect(showCelebration ? 1.2 : 1.0)
                                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: showCelebration)
                            }
                            Spacer()
                        }
                    }

                    // Subtle celebration pulse
                    if showCelebration {
                        Circle()
                            .stroke(AppTheme.sexyGradient, lineWidth: 1)
                            .frame(width: 66, height: 66)
                            .scaleEffect(1.05)
                            .opacity(0.6)
                            .animation(.easeOut(duration: 0.4), value: showCelebration)
                    }
                }

                // Clean name display with better sizing
                Text(profileLoader.user?.firstName ?? "...")
                    .font(.system(size: 11, weight: .semibold))
                    .foregroundColor(AppTheme.textPrimary)
                    .lineLimit(1)
                    .truncationMode(.tail)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: 70)
            }
        }
        .frame(width: 70, height: 90) // Fixed size to prevent overflow
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.92 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .onAppear {
            // Use safe profile loading to avoid publishing warnings
            profileLoader.loadProfile(candidateID: candidateID)
            // Start shimmer animation
            withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                shimmerOffset = 200
            }
        }
        .onChange(of: showCelebration) { _, celebrating in
            if celebrating {
                // Auto-reset celebration
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                    showCelebration = false
                }
            }
        }
    }
}

// MARK: - Enhanced Horizontal Match Card Component

struct EnhancedHorizontalMatchCard: View {
    let candidateID: String
    let isNewMatch: Bool

    @ObservedObject private var profileLoader = ProfileLoaderService.shared
    @State private var isPressed = false

    var body: some View {
        VStack(spacing: AppTheme.spacing12) {
            ZStack {
                // Enhanced profile image with gradient background
                if let imageUrl = profileLoader.user?.profileImageUrls?.first,
                   let url = URL(string: imageUrl) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .scaledToFill()
                            .frame(width: 80, height: 80)
                            .clipped()
                    } placeholder: {
                        ZStack {
                            Circle()
                                .fill(AppTheme.primaryGradient)

                            ProgressView()
                                .tint(.white)
                        }
                    }
                    .frame(width: 80, height: 80)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(AppTheme.cardBackground, lineWidth: 3)
                    )
                    .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)
                } else {
                    ZStack {
                        Circle()
                            .fill(AppTheme.primaryGradient)
                            .frame(width: 80, height: 80)

                        Image(systemName: "person.fill")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .overlay(
                        Circle()
                            .stroke(AppTheme.cardBackground, lineWidth: 3)
                    )
                    .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)
                }

                // Enhanced new match indicator
                if isNewMatch {
                    VStack {
                        HStack {
                            Spacer()
                            ZStack {
                                Circle()
                                    .fill(AppTheme.accentColor)
                                    .frame(width: 20, height: 20)

                                Image(systemName: "star.fill")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            .shadow(color: AppTheme.accentColor.opacity(0.4), radius: 4, x: 0, y: 2)
                        }
                        Spacer()
                    }
                }

                // Enhanced online status indicator
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Circle()
                            .fill(AppTheme.successColor)
                            .frame(width: 16, height: 16)
                            .overlay(
                                Circle()
                                    .stroke(AppTheme.cardBackground, lineWidth: 2)
                            )
                            .shadow(color: AppTheme.successColor.opacity(0.3), radius: 3, x: 0, y: 1)
                    }
                }
            }

            // Enhanced name display
            Text(profileLoader.user?.firstName ?? "Loading...")
                .font(AppTheme.caption)
                .fontWeight(.medium)
                .foregroundColor(AppTheme.textPrimary)
                .lineLimit(1)
        }
        .frame(width: 90, height: 110) // Fixed size to prevent overflow
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: AppTheme.animationFast), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .onAppear {
            // Use safe profile loading to avoid publishing warnings
            profileLoader.loadProfile(candidateID: candidateID)
        }
    }
}

// MARK: - Enhanced Chat List Card Component

struct EnhancedChatListCard: View {
    let chat: ChatListItem
    let currentUserID: String
    let profileLoader: ProfileLoaderService

    @State private var isPressed = false
    @State private var shimmerOffset: CGFloat = -200

    var body: some View {
        NavigationLink(destination: destinationView) {
            HStack(spacing: AppTheme.spacing16) {
                // Enhanced Profile Image with beautiful styling
                ZStack {
                    profileImageView

                    // Online status indicator with glow
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Circle()
                                .fill(AppTheme.successColor)
                                .frame(width: 18, height: 18)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 3)
                                )
                                .shadow(color: AppTheme.successColor.opacity(0.6), radius: 4, x: 0, y: 2)
                        }
                    }
                }

                // Enhanced Chat Content with beautiful typography
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    // Name and timestamp with enhanced styling
                    HStack {
                        Text(profileLoader.user?.firstName ?? "Loading...")
                            .font(.custom("AvenirNext-Bold", size: 18))
                            .foregroundStyle(AppTheme.sexyGradient)

                        Spacer()

                        Text(timeAgoString)
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    // Last message preview with enhanced styling
                    HStack {
                        Text(lastMessagePreview)
                            .font(.custom("AvenirNext-Medium", size: 15))
                            .foregroundColor(unreadCount > 0 ? AppTheme.textPrimary : AppTheme.textSecondary)
                            .fontWeight(unreadCount > 0 ? .semibold : .regular)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        Spacer()

                        // Enhanced unread indicator with glow
                        if unreadCount > 0 {
                            ZStack {
                                Circle()
                                    .fill(AppTheme.sexyGradient)
                                    .frame(width: 28, height: 28)
                                    .neonGlow(color: AppTheme.primaryColor)

                                Text("\(unreadCount)")
                                    .font(.custom("AvenirNext-Bold", size: 12))
                                    .foregroundColor(.white)
                                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                            }
                        }
                    }
                }
            }
            .padding(AppTheme.spacing20)
            .background(
                ZStack {
                    // Enhanced glass morphism background
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.modernCardGradient)

                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.glassEffect)

                    // Subtle shimmer effect
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.clear,
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .offset(x: shimmerOffset)
                        .animation(
                            .linear(duration: 2.0)
                            .repeatForever(autoreverses: false),
                            value: shimmerOffset
                        )
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.4),
                                unreadCount > 0 ? AppTheme.primaryColor.opacity(0.6) : Color.white.opacity(0.2),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1.5
                    )
            )
            .floatingCard()
            .scaleEffect(isPressed ? 0.97 : 1.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .onAppear {
            loadOtherUserProfile()
            // Start shimmer animation
            withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                shimmerOffset = 200
            }
        }
    }

    // MARK: - Computed Properties

    private var otherUserID: String? {
        chat.getOtherParticipant(currentUserID: currentUserID)
    }

    private var unreadCount: Int {
        chat.getUnreadCount(for: currentUserID)
    }

    private var lastMessagePreview: String {
        if chat.lastMessage.isEmpty {
            return "Tap to start your conversation..."
        }

        let isFromCurrentUser = chat.lastMessageSenderID == currentUserID
        let prefix = isFromCurrentUser ? "You: " : ""
        return prefix + chat.lastMessage
    }

    private var timeAgoString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: chat.lastMessageAt, relativeTo: Date())
    }

    private var profileImageView: some View {
        Group {
            if let profileImageUrl = profileLoader.user?.profileImageUrls?.first,
               let url = URL(string: profileImageUrl) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .scaledToFill()
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(AppTheme.sexyGradient)

                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    }
                    .shimmerEffect()
                }
            } else {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)

                    Image(systemName: "person.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                }
                .shimmerEffect()
            }
        }
        .frame(width: 70, height: 70)
        .clipShape(Circle())
        .overlay(
            Circle()
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.8),
                            AppTheme.primaryColor.opacity(0.4),
                            Color.white.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2.5
                )
        )
        .neonGlow(color: AppTheme.primaryColor)
    }

    private var destinationView: some View {
        Group {
            if let otherUserID = otherUserID {
                ChatConversationView(
                    viewModel: ChatConversationViewModel(chatID: chat.id),
                    chatPartnerID: otherUserID
                )
            } else {
                Text("Error: Unable to load chat")
                    .foregroundColor(.red)
            }
        }
    }

    // MARK: - Methods

    private func loadOtherUserProfile() {
        guard let otherUserID = otherUserID else {
            print("⚠️ EnhancedChatListCard: No other user ID found for chat")
            return
        }

        // Check if profile is already cached
        if let cachedProfile = ProfileLoaderService.shared.getCachedProfile(userID: otherUserID) {
            print("✅ EnhancedChatListCard: Using cached profile for user: \(cachedProfile.firstName ?? "Unknown")")
            profileLoader.user = cachedProfile
        } else {
            print("🔄 EnhancedChatListCard: Loading profile for user: \(otherUserID)")
            // Use safe profile loading to avoid publishing warnings
            profileLoader.loadProfile(candidateID: otherUserID)
        }
    }
}

struct CombinedMatchesChatView_Previews: PreviewProvider {
    static var previews: some View {
        CombinedMatchesChatView()
    }
}
