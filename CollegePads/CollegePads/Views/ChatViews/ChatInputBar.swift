import SwiftUI

enum MediaOption {
    case camera
    case photoLibrary
    case document
}

struct ChatInputBar: View {
    @Binding var messageText: String
    var onSend: () -> Void
    var onMediaSelected: (MediaOption) -> Void

    @State private var showMediaOptions: Bool = false
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        VStack(spacing: 0) {
            // Separator line
            Rectangle()
                .fill(AppTheme.surfaceSecondary)
                .frame(height: 1)

            HStack(spacing: AppTheme.spacing12) {
                // Enhanced media sharing button
                But<PERSON>(action: {
                    showMediaOptions = true
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                }) {
                    Image(systemName: "plus.circle.fill")
                }
                .buttonStyle(IconButtonStyle(
                    backgroundColor: AppTheme.surfaceSecondary,
                    iconColor: AppTheme.primaryColor,
                    size: 36
                ))
                .confirmationDialog("Share Media", isPresented: $showMediaOptions, titleVisibility: .visible) {
                    But<PERSON>("Take Photo") {
                        onMediaSelected(.camera)
                    }
                    <PERSON><PERSON>("Choose from Library") {
                        onMediaSelected(.photoLibrary)
                    }
                    <PERSON><PERSON>("Share Document") {
                        onMediaSelected(.document)
                    }
                    Button("Cancel", role: .cancel) { }
                }

                // Enhanced text input with modern effects
                HStack(spacing: AppTheme.spacing8) {
                    TextField("Type a message...", text: $messageText, axis: .vertical)
                        .font(AppTheme.body)
                        .focused($isTextFieldFocused)
                        .lineLimit(1...4)
                        .padding(.horizontal, AppTheme.spacing12)
                        .padding(.vertical, AppTheme.spacing8)
                        .onAppear {
                            // Auto-focus text field when chat opens for better UX
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                                isTextFieldFocused = true
                            }
                        }
                        .background(
                            ZStack {
                                AppTheme.surfaceSecondary

                                // Subtle inner glow when focused
                                if isTextFieldFocused {
                                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                                        .fill(AppTheme.primaryColor.opacity(0.05))
                                        .blur(radius: 2)
                                }
                            }
                        )
                        .cornerRadius(AppTheme.radiusLarge)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                                .stroke(
                                    isTextFieldFocused ?
                                    LinearGradient(
                                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    ) :
                                    LinearGradient(colors: [Color.clear], startPoint: .leading, endPoint: .trailing),
                                    lineWidth: isTextFieldFocused ? 2 : 0
                                )
                        )
                        .scaleEffect(isTextFieldFocused ? 1.02 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isTextFieldFocused)
                }

                // Enhanced send button with modern effects
                Button(action: {
                    guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
                    onSend()
                    HapticFeedbackManager.shared.generateImpact(style: .medium)
                }) {
                    Image(systemName: messageText.isEmpty ? "paperplane" : "paperplane.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 36, height: 36)
                        .background(
                            ZStack {
                                Circle()
                                    .fill(
                                        messageText.isEmpty ?
                                        LinearGradient(gradient: Gradient(colors: [AppTheme.textTertiary]), startPoint: .leading, endPoint: .trailing) :
                                        LinearGradient(
                                            colors: [AppTheme.primaryColor, AppTheme.accentColor, AppTheme.primaryColor.opacity(0.8)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )

                                // Subtle inner glow for active state
                                if !messageText.isEmpty {
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                        .blur(radius: 1)
                                }
                            }
                        )
                        .scaleEffect(messageText.isEmpty ? 0.9 : 1.0)
                        .shadow(color: messageText.isEmpty ? .clear : AppTheme.primaryColor.opacity(0.4), radius: 8, x: 0, y: 4)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: messageText.isEmpty)
                }
                .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            .padding(.horizontal, AppTheme.spacing16)
            .padding(.vertical, AppTheme.spacing12)
            .background(AppTheme.surfacePrimary)
        }
    }
}

struct ChatInputBar_Previews: PreviewProvider {
    static var previews: some View {
        ChatInputBar(messageText: .constant("Type a message..."), onSend: {}, onMediaSelected: { _ in })
            .previewLayout(.sizeThatFits)
    }
}
