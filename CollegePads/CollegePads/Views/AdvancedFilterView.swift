// AdvancedFilterView.swift

import SwiftUI
import CoreLocation

struct AdvancedFilterView: View {
    @StateObject private var viewModel = AdvancedFilterViewModel()
    @StateObject private var locationManager = LocationManager()

    let preferredGenders = ["Any", "Male", "Female", "Other"]
    @State private var selectedGradeLevel: MyProfileView.GradeLevel = .freshman

    /// allColleges holds the full list, loaded once on appear
    @State private var allColleges: [String] = []

    // College search state:
    @State private var collegeSearchQuery: String = ""
    @State private var filteredColleges: [String] = []

    // Animation states
    @State private var animateCards = false
    @State private var cardAnimationDelay: Double = 0

    private var alertBinding: Binding<GenericAlertError?> {
        Binding(
            get: { viewModel.errorMessage.map(GenericAlertError.init) },
            set: { _ in viewModel.errorMessage = nil }
        )
    }


    var body: some View {
        ZStack {
            // Sexy dynamic background
            AppTheme.dynamicBackgroundGradient.ignoresSafeArea()

            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: AppTheme.spacing20) {
                    // Top spacing for navigation
                    Spacer()
                        .frame(height: 20)

                    // Filter Mode Section
                    filterModeSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)

                    // Filter Criteria Sections
                    gradeAndHousingSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)

                    amenitiesSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)

                    lifestyleSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.4), value: animateCards)

                    locationSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateCards)

                    preferencesSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.6), value: animateCards)

                    // Bottom spacing for safe area
                    Spacer()
                        .frame(height: 100)
                }
                .padding(.horizontal, AppTheme.spacing16)
            }
            .alert(item: alertBinding) { err in
                Alert(title: Text("Error"), message: Text(err.message), dismissButton: .default(Text("OK")))
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("Advanced Search")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)
                }
            }
        }
        .onAppear {
            // 1️⃣ Load saved filters & sync UI state
            viewModel.loadFiltersFromUserDoc {
                selectedGradeLevel = MyProfileView.GradeLevel(
                    rawValue: viewModel.filterGradeGroup
                ) ?? .freshman

                collegeSearchQuery = viewModel.filterCollegeName

                viewModel.applyFilters(
                    currentLocation: locationManager.currentLocation
                )
            }
            // 2️⃣ Then load the college list
            UniversityDataProvider.shared.loadUniversities { colleges in
                allColleges = colleges
            }

            // 3️⃣ Trigger entrance animations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                animateCards = true
            }
        }
    }

    private func autoApplyAndSave() {
        viewModel.applyFilters(currentLocation: locationManager.currentLocation)
        viewModel.saveFiltersToUserDoc()
    }

    // MARK: - Modern Section Views

    private var filterModeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header with icon
            sectionHeader(
                icon: "slider.horizontal.3",
                title: "Filter Mode",
                subtitle: "Choose how to discover roommates"
            )

            // Modern segmented picker
            filterModeButtons
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var filterModeButtons: some View {
        HStack(spacing: 0) {
            ForEach(FilterMode.allCases, id: \.self) { mode in
                filterModeButton(for: mode)
            }
        }
    }

    private func filterModeButton(for mode: FilterMode) -> some View {
        let isSelected = viewModel.filterMode == mode
        let title = mode == .university ? "By College" : "By Distance"

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterMode = mode
            if mode == .university {
                viewModel.maxDistance = 50.0
            } else if mode == .distance {
                viewModel.filterCollegeName = ""
            }
            autoApplyAndSave()
        }) {
            Text(title)
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                .padding(.vertical, AppTheme.spacing12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var gradeAndHousingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "graduationcap.fill",
                title: "Academic & Housing",
                subtitle: "Your grade level and housing preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                gradeLevelPicker
                housingPreferencePicker
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var gradeLevelPicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Grade Level")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            Menu {
                ForEach(MyProfileView.GradeLevel.allCases) { level in
                    Button(level.rawValue) {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        viewModel.filterGradeGroup = level.rawValue
                        autoApplyAndSave()
                    }
                }
            } label: {
                HStack {
                    Text(viewModel.filterGradeGroup.isEmpty ? "Select Grade" : viewModel.filterGradeGroup)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(.white)

                    Spacer()

                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
        }
    }

    private var housingPreferencePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Housing Status")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            VStack(spacing: AppTheme.spacing8) {
                ForEach([Optional<PrimaryHousingPreference>(nil)] + PrimaryHousingPreference.allCases.map(Optional.init), id: \.self) { option in
                    housingOptionButton(for: option)
                }
            }
        }
    }

    private func housingOptionButton(for option: PrimaryHousingPreference?) -> some View {
        let isSelected = (option == nil && viewModel.showAllProfiles) ||
                       (!viewModel.showAllProfiles && viewModel.filterHousingPreference == option)
        let title = option?.rawValue ?? "All"

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            if option == nil {
                viewModel.showAllProfiles = true
                viewModel.filterHousingPreference = nil
            } else {
                viewModel.showAllProfiles = false
                viewModel.filterHousingPreference = option
            }
            autoApplyAndSave()
        }) {
            HStack {
                Text(title)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var amenitiesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header with count
            amenitiesSectionHeader

            VStack(spacing: AppTheme.spacing16) {
                // Amenities chips
                EnhancedMultiSelectChipView(
                    options: viewModel.propertyAmenitiesOptions,
                    selectedItems: $viewModel.filterAmenities,
                    onSelectionChanged: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        autoApplyAndSave()
                    }
                )

                // Lifestyle toggles
                lifestyleToggles
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var amenitiesSectionHeader: some View {
        HStack {
            Image(systemName: "star.circle.fill")
                .font(.system(size: 24, weight: .bold))
                .foregroundStyle(AppTheme.sexyGradient)

            VStack(alignment: .leading, spacing: 4) {
                Text("Amenities & Lifestyle")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Text("Property features and lifestyle preferences")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()

            Text("\(viewModel.filterAmenities.count) selected")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.7))
        }
    }

    private var lifestyleToggles: some View {
        VStack(spacing: AppTheme.spacing12) {
            EnhancedToggleView(
                title: "Pet Friendly",
                subtitle: "Must allow pets",
                icon: "pawprint.fill",
                isOn: Binding(
                    get: { viewModel.filterPetFriendly ?? false },
                    set: {
                        viewModel.filterPetFriendly = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Smoker OK",
                subtitle: "Comfortable with smoking",
                icon: "smoke.fill",
                isOn: Binding(
                    get: { viewModel.filterSmoker ?? false },
                    set: {
                        viewModel.filterSmoker = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Drinker OK",
                subtitle: "Comfortable with drinking",
                icon: "wineglass.fill",
                isOn: Binding(
                    get: { viewModel.filterDrinker ?? false },
                    set: {
                        viewModel.filterDrinker = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Marijuana Use OK",
                subtitle: "Comfortable with marijuana",
                icon: "leaf.fill",
                isOn: Binding(
                    get: { viewModel.filterMarijuana ?? false },
                    set: {
                        viewModel.filterMarijuana = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Workout Regularly",
                subtitle: "Active fitness lifestyle",
                icon: "dumbbell.fill",
                isOn: Binding(
                    get: { viewModel.filterWorkout ?? false },
                    set: {
                        viewModel.filterWorkout = $0
                        autoApplyAndSave()
                    }
                )
            )
        }
    }

    private var lifestyleSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "moon.stars.fill",
                title: "Lifestyle Preferences",
                subtitle: "Cleanliness and sleep schedule preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                cleanlinessPicker
                sleepSchedulePicker
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var cleanlinessPicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Cleanliness Level")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            Menu {
                ForEach(1..<6) { level in
                    Button("\(level) – \(viewModel.cleanlinessDescriptions[level] ?? "")") {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        viewModel.filterCleanliness = level
                        autoApplyAndSave()
                    }
                }
            } label: {
                HStack {
                    if let cleanliness = viewModel.filterCleanliness, cleanliness > 0 {
                        Text("\(cleanliness) – \(viewModel.cleanlinessDescriptions[cleanliness] ?? "")")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                    } else {
                        Text("Select Cleanliness Level")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white.opacity(0.7))
                    }

                    Spacer()

                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
        }
    }

    private var sleepSchedulePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Sleep Schedule")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            let sleepOptions = ["All", "Early Bird", "Night Owl", "Flexible"]
            VStack(spacing: AppTheme.spacing8) {
                ForEach(sleepOptions, id: \.self) { option in
                    sleepScheduleButton(for: option)
                }
            }
        }
    }

    private func sleepScheduleButton(for option: String) -> some View {
        let isSelected = (option == "All" && viewModel.filterSleepSchedule.isEmpty) ||
                       viewModel.filterSleepSchedule == option

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterSleepSchedule = option == "All" ? "" : option
            autoApplyAndSave()
        }) {
            HStack {
                Text(option)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var locationSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            HStack {
                Image(systemName: "location.circle.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Location & Housing")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)

                    Text("College and distance preferences")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()
            }

            VStack(spacing: AppTheme.spacing16) {
                // College search (if university mode)
                if viewModel.filterMode == .university {
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("College")
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)

                        TextField("Search College", text: $collegeSearchQuery)
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                            .disableAutocorrection(true)
                            .textInputAutocapitalization(.never)
                            .padding(AppTheme.spacing12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(.ultraThinMaterial.opacity(0.6))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                            .onChange(of: collegeSearchQuery) { _, q in
                                let trimmed = q.trimmingCharacters(in: .whitespaces)
                                filteredColleges = trimmed.isEmpty
                                    ? []
                                    : UniversityDataProvider.shared.searchUniversities(query: trimmed)
                            }

                        if !filteredColleges.isEmpty {
                            ScrollView {
                                VStack(alignment: .leading, spacing: 4) {
                                    ForEach(filteredColleges, id: \.self) { college in
                                        Button(college) {
                                            HapticFeedbackManager.shared.generateImpact(style: .light)
                                            collegeSearchQuery = college
                                            viewModel.filterCollegeName = college
                                            filteredColleges = []
                                            autoApplyAndSave()
                                        }
                                        .foregroundColor(.white)
                                        .padding(AppTheme.spacing8)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(.ultraThinMaterial.opacity(0.4))
                                        )
                                    }
                                }
                            }
                            .frame(maxHeight: 150)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(.ultraThinMaterial.opacity(0.8))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                    }
                }

                // Distance slider (if distance mode)
                if viewModel.filterMode == .distance {
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        HStack {
                            Text("Max Distance")
                                .font(.custom("AvenirNext-Bold", size: 16))
                                .foregroundColor(.white)

                            Spacer()

                            Text("\(Int(viewModel.maxDistance)) km")
                                .font(.custom("AvenirNext-Bold", size: 16))
                                .foregroundColor(.white)
                                .padding(.horizontal, AppTheme.spacing12)
                                .padding(.vertical, AppTheme.spacing6)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(AppTheme.sexyGradient)
                                )
                        }

                        EnhancedSlider(
                            value: Binding(
                                get: { viewModel.maxDistance },
                                set: {
                                    viewModel.maxDistance = $0
                                    autoApplyAndSave()
                                }
                            ),
                            range: 0...100,
                            step: 1
                        )
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
        .modernCardStyle()
    }

    private var preferencesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "person.2.circle.fill",
                title: "Personal Preferences",
                subtitle: "Gender and age preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                genderPreferencePicker
                ageDifferenceSlider
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var genderPreferencePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Preferred Gender")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            VStack(spacing: AppTheme.spacing8) {
                ForEach(preferredGenders, id: \.self) { gender in
                    genderOptionButton(for: gender)
                }
            }
        }
    }

    private func genderOptionButton(for gender: String) -> some View {
        let isSelected = viewModel.filterPreferredGender == gender

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterPreferredGender = gender
            autoApplyAndSave()
        }) {
            HStack {
                Text(gender)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var ageDifferenceSlider: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Text("Max Age Difference")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Spacer()

                Text("\(Int(viewModel.maxAgeDifference)) years")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, AppTheme.spacing12)
                    .padding(.vertical, AppTheme.spacing6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(AppTheme.sexyGradient)
                    )
            }

            EnhancedSlider(
                value: $viewModel.maxAgeDifference,
                range: 0...10,
                step: 1,
                onEditingChanged: { _ in
                    autoApplyAndSave()
                }
            )
        }
    }

    // MARK: - Helper Methods

    private func sectionHeader(icon: String, title: String, subtitle: String) -> some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()
        }
    }

    private var modernCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }

    private var modernInputBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial.opacity(0.6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Enhanced UI Components

struct EnhancedMultiSelectChipView: View {
    let options: [String]
    @Binding var selectedItems: [String]
    var onSelectionChanged: () -> Void = {}
    var maxSelection: Int? = nil

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 120), spacing: AppTheme.spacing8)
        ], spacing: AppTheme.spacing8) {
            ForEach(options, id: \.self) { option in
                chipView(option)
            }
        }
    }

    private func chipView(_ item: String) -> some View {
        let isSelected = selectedItems.contains(item)
        let isDisabled = !isSelected && (maxSelection != nil && selectedItems.count >= maxSelection!)

        return Button(action: {
            if isDisabled { return }
            HapticFeedbackManager.shared.generateImpact(style: .light)
            if isSelected {
                selectedItems.removeAll { $0 == item }
            } else {
                selectedItems.append(item)
            }
            onSelectionChanged()
        }) {
            Text(item)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(isSelected ? .white : .white.opacity(0.8))
                .padding(.vertical, AppTheme.spacing8)
                .padding(.horizontal, AppTheme.spacing12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .opacity(isDisabled ? 0.5 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct EnhancedToggleView: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool

    var body: some View {
        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            isOn.toggle()
        }) {
            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(isOn ? .white : .white.opacity(0.6))
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(isOn ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isOn ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                        .frame(width: 50, height: 30)

                    Circle()
                        .fill(.white)
                        .frame(width: 24, height: 24)
                        .offset(x: isOn ? 10 : -10)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isOn)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isOn ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isOn)
    }
}

struct EnhancedSlider: View {
    @Binding var value: Double
    let range: ClosedRange<Double>
    let step: Double
    var onEditingChanged: ((Bool) -> Void)? = nil

    @State private var isDragging = false

    var body: some View {
        VStack(spacing: AppTheme.spacing8) {
            Slider(
                value: $value,
                in: range,
                step: step,
                onEditingChanged: { editing in
                    isDragging = editing
                    onEditingChanged?(editing)
                    if editing {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                    }
                }
            )
            .accentColor(.white)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial.opacity(0.4))
                    .frame(height: 8)
            )
            .scaleEffect(isDragging ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isDragging)
        }
    }
}
